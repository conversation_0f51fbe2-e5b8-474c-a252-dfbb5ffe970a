#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试核心功能
"""

import sys
import os
import json
import time
from pathlib import Path

# 添加tools目录到路径
sys.path.append(str(Path(__file__).parent / "tools"))

def test_browser_manager():
    """测试浏览器管理器"""
    print("🔧 测试浏览器管理器...")
    
    try:
        from browser_manager import BrowserManager
        manager = BrowserManager()
        print("✅ BrowserManager 导入成功")
        
        # 测试列出实例
        instances = manager.list_instances()
        print(f"✅ 找到 {len(instances)} 个实例:")
        
        for instance in instances:
            name = instance.get('name', 'Unknown')
            config = instance.get('config', {})
            display_name = config.get('display_name', name)
            icon = config.get('icon', 'default')
            description = config.get('description', '')
            
            print(f"  - {name}: {display_name} ({icon}) - {description}")
        
        # 测试运行状态检查
        print("\n🔍 检查实例运行状态:")
        for instance in instances:
            name = instance.get('name')
            if name:
                is_running = manager.is_instance_running(name)
                status = "运行中" if is_running else "已停止"
                print(f"  - {name}: {status}")
        
        return True
        
    except Exception as e:
        print(f"❌ BrowserManager 测试失败: {e}")
        return False

def test_icon_manager():
    """测试图标管理器"""
    print("\n🎨 测试图标管理器...")
    
    try:
        from icon_manager import IconManager
        manager = IconManager()
        print("✅ IconManager 导入成功")
        
        # 测试获取图标列表
        icons = manager.get_available_icons()
        print(f"✅ 找到 {len(icons)} 个图标:")
        
        for icon in icons:
            print(f"  - {icon}")
        
        return True
        
    except Exception as e:
        print(f"❌ IconManager 测试失败: {e}")
        return False

def test_create_and_delete():
    """测试创建和删除实例"""
    print("\n🧪 测试创建和删除实例...")
    
    try:
        from browser_manager import BrowserManager
        manager = BrowserManager()
        
        # 创建测试实例
        test_name = f"测试实例_{int(time.time())}"
        test_config = {
            "display_name": "测试浏览器",
            "description": "这是一个测试实例",
            "icon": "default"
        }
        
        print(f"📝 创建实例: {test_name}")
        success = manager.create_instance(test_name, test_config)
        
        if success:
            print("✅ 实例创建成功")
            
            # 验证实例存在
            instances = manager.list_instances()
            test_instance = next((inst for inst in instances if inst['name'] == test_name), None)
            
            if test_instance:
                print("✅ 实例验证成功")
                
                # 删除测试实例
                print(f"🗑️  删除实例: {test_name}")
                delete_success = manager.delete_instance(test_name)
                
                if delete_success:
                    print("✅ 实例删除成功")
                    return True
                else:
                    print("❌ 实例删除失败")
                    return False
            else:
                print("❌ 实例验证失败")
                return False
        else:
            print("❌ 实例创建失败")
            return False
            
    except Exception as e:
        print(f"❌ 创建删除测试失败: {e}")
        return False

def test_file_structure():
    """测试文件结构"""
    print("\n📁 测试文件结构...")
    
    required_files = [
        "tools/browser_manager.py",
        "tools/icon_manager.py",
        "templates/index.html",
        "static/css/style.css",
        "static/js/app.js",
        "启动现代化界面.py"
    ]
    
    all_exist = True
    for file_path in required_files:
        if Path(file_path).exists():
            size = Path(file_path).stat().st_size
            print(f"✅ {file_path} ({size} 字节)")
        else:
            print(f"❌ {file_path} (不存在)")
            all_exist = False
    
    return all_exist

def test_static_images():
    """测试静态图片"""
    print("\n🖼️  测试静态图片...")
    
    static_dir = Path("static/images")
    if not static_dir.exists():
        print("❌ static/images 目录不存在")
        return False
    
    icon_files = list(static_dir.glob("*.ico"))
    print(f"✅ 找到 {len(icon_files)} 个图标文件:")
    
    for icon_file in icon_files:
        size = icon_file.stat().st_size
        print(f"  - {icon_file.name} ({size} 字节)")
    
    return len(icon_files) > 0

def main():
    """主函数"""
    print("=" * 60)
    print("🧪 核心功能测试")
    print("=" * 60)
    
    tests = [
        ("文件结构", test_file_structure),
        ("静态图片", test_static_images),
        ("浏览器管理器", test_browser_manager),
        ("图标管理器", test_icon_manager),
        ("创建删除功能", test_create_and_delete)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 统计结果
    print("\n" + "=" * 60)
    print("📊 测试结果:")
    print("=" * 60)
    
    total = len(results)
    passed = sum(1 for _, success in results if success)
    failed = total - passed
    
    print(f"总测试: {total}")
    print(f"通过: {passed}")
    print(f"失败: {failed}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if failed > 0:
        print("\n❌ 失败的测试:")
        for test_name, success in results:
            if not success:
                print(f"  - {test_name}")
    
    print("\n" + "=" * 60)
    if failed == 0:
        print("🎉 所有核心功能测试通过！")
        print("\n💡 现代化界面状态:")
        print("✅ 所有核心组件正常")
        print("✅ 文件结构完整")
        print("✅ 浏览器管理功能正常")
        print("✅ 图标管理功能正常")
        print("✅ 实例创建删除功能正常")
        print("\n🚀 可以正常使用现代化界面:")
        print("   python 启动现代化界面.py")
        print("   http://127.0.0.1:8000")
    else:
        print("⚠️  部分功能存在问题，需要修复")
    print("=" * 60)
    
    return failed == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
