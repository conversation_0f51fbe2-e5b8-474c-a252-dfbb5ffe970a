/* 现代化浏览器管理界面样式 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background-color: #f5f6fa;
    color: #2c3e50;
    line-height: 1.6;
}

/* 侧边栏样式 */
.sidebar {
    position: fixed;
    left: 0;
    top: 0;
    width: 200px;
    height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    z-index: 1000;
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 18px;
    font-weight: 600;
}

.sidebar-nav ul {
    list-style: none;
    padding: 20px 0;
}

.nav-item {
    margin: 5px 0;
}

.nav-item a {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 20px;
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    transition: all 0.3s ease;
}

.nav-item a:hover,
.nav-item.active a {
    background: rgba(255,255,255,0.1);
    color: white;
    border-right: 3px solid #74b9ff;
}

/* 主内容区 */
.main-content {
    margin-left: 200px;
    min-height: 100vh;
    background: white;
}

/* 工具栏 */
.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 30px;
    background: white;
    border-bottom: 1px solid #e1e8ed;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.toolbar-left,
.toolbar-right {
    display: flex;
    align-items: center;
    gap: 15px;
}

/* 按钮样式 */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    text-decoration: none;
}

.btn-primary {
    background: #6c5ce7;
    color: white;
}

.btn-primary:hover {
    background: #5f3dc4;
    transform: translateY(-1px);
}

.btn-secondary {
    background: #74b9ff;
    color: white;
}

.btn-secondary:hover {
    background: #0984e3;
}

.btn-info {
    background: #00b894;
    color: white;
}

.btn-info:hover {
    background: #00a085;
}

.btn-outline {
    background: transparent;
    color: #636e72;
    border: 1px solid #ddd;
}

.btn-outline:hover {
    background: #f8f9fa;
    border-color: #6c5ce7;
    color: #6c5ce7;
}

.btn-danger {
    background: #fd79a8;
    color: white;
}

.btn-danger:hover {
    background: #e84393;
}

.btn-success {
    background: #00b894;
    color: white;
}

.btn-success:hover {
    background: #00a085;
}

/* 下拉菜单 */
.dropdown {
    position: relative;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: white;
    border: 1px solid #e1e8ed;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    min-width: 150px;
    z-index: 1000;
    display: none;
}

.dropdown-menu.show {
    display: block;
}

.dropdown-item {
    display: block;
    padding: 10px 15px;
    color: #2c3e50;
    text-decoration: none;
    transition: background 0.2s;
}

.dropdown-item:hover {
    background: #f8f9fa;
}

/* 搜索框 */
.search-box {
    position: relative;
    width: 250px;
}

.search-box input {
    width: 100%;
    padding: 8px 35px 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
}

.search-box i {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #636e72;
}

/* 表格样式 */
.table-container {
    margin: 20px 30px;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th {
    background: #f8f9fa;
    padding: 15px 12px;
    text-align: left;
    font-weight: 600;
    color: #2c3e50;
    border-bottom: 2px solid #e1e8ed;
}

.data-table td {
    padding: 15px 12px;
    border-bottom: 1px solid #f1f3f4;
    vertical-align: middle;
}

.data-table tbody tr:hover {
    background: #f8f9fa;
}

/* 状态标签 */
.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.status-running {
    background: #d1f2eb;
    color: #00b894;
}

.status-stopped {
    background: #ffeaa7;
    color: #fdcb6e;
}

/* 分组标签 */
.group-tag {
    background: #e17055;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
}

/* 分页样式 */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 30px;
    background: white;
    border-top: 1px solid #e1e8ed;
}

.pagination {
    display: flex;
    align-items: center;
    gap: 10px;
}

.page-btn {
    padding: 6px 12px;
    border: 1px solid #ddd;
    background: white;
    color: #636e72;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
}

.page-btn:hover:not(:disabled) {
    border-color: #6c5ce7;
    color: #6c5ce7;
}

.page-btn.active {
    background: #6c5ce7;
    color: white;
    border-color: #6c5ce7;
}

.page-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.page-jump {
    margin-left: 20px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.page-jump input {
    width: 60px;
    padding: 4px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-align: center;
}

/* 模态框样式 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 2000;
}

.modal.show {
    display: flex;
}

.modal-content {
    background: white;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #e1e8ed;
}

.modal-header h3 {
    margin: 0;
    color: #2c3e50;
}

.modal-close {
    background: none;
    border: none;
    font-size: 18px;
    color: #636e72;
    cursor: pointer;
    padding: 5px;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 20px;
    border-top: 1px solid #e1e8ed;
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #2c3e50;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #6c5ce7;
    box-shadow: 0 0 0 3px rgba(108, 92, 231, 0.1);
}

/* 加载动画 */
.loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255,255,255,0.9);
    display: none;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    z-index: 3000;
}

.loading.show {
    display: flex;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #6c5ce7;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    margin-top: 15px;
    color: #636e72;
    font-size: 14px;
}

/* 消息提示 */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    padding: 15px 20px;
    display: none;
    align-items: center;
    gap: 10px;
    z-index: 4000;
    min-width: 300px;
}

.toast.show {
    display: flex;
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.toast.success {
    border-left: 4px solid #00b894;
}

.toast.error {
    border-left: 4px solid #fd79a8;
}

.toast.warning {
    border-left: 4px solid #fdcb6e;
}

.toast.info {
    border-left: 4px solid #74b9ff;
}

/* 图标显示 */
.icon-display {
    width: 24px;
    height: 24px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    display: inline-block;
}

/* 操作按钮组 */
.action-buttons {
    display: flex;
    gap: 5px;
}

.action-buttons .btn {
    padding: 4px 8px;
    font-size: 12px;
}

/* 复选框样式 */
input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: #6c5ce7;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    .toolbar {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .toolbar-left,
    .toolbar-right {
        justify-content: center;
    }

    .search-box {
        width: 100%;
    }

    .table-container {
        margin: 10px;
        overflow-x: auto;
    }

    .pagination-container {
        flex-direction: column;
        gap: 15px;
    }
}
