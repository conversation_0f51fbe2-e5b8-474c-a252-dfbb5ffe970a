#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI界面演示脚本
展示GUI界面的主要功能
"""

import sys
import os
import time
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def demo_gui():
    """演示GUI界面"""
    print("=" * 60)
    print("🖥️  浏览器多账号绿色版 - GUI界面演示")
    print("=" * 60)
    
    print("\n📋 功能清单:")
    print("✅ 图形化主界面 - 友好的桌面应用程序")
    print("✅ 实例卡片显示 - 直观展示所有浏览器实例")
    print("✅ 双击启动功能 - 双击实例卡片即可启动")
    print("✅ 实时状态监控 - 显示实例运行状态")
    print("✅ 新建实例对话框 - 可视化创建新实例")
    print("✅ 批量配置对话框 - 使用模板批量创建")
    print("✅ 工具栏操作 - 刷新、新建、配置等功能")
    print("✅ 状态栏信息 - 显示实例统计和操作状态")
    
    print("\n🎯 界面特色:")
    print("• 卡片式布局 - 每个实例以卡片形式展示")
    print("• 图标支持 - 支持自定义图标显示")
    print("• 状态指示 - 实时显示运行状态（绿色=运行中）")
    print("• 右键菜单 - 右键实例显示管理选项")
    print("• 响应式设计 - 界面大小可调整")
    
    print("\n🚀 启动方式:")
    print("1. 双击 '启动GUI界面.bat' (Windows推荐)")
    print("2. 运行 'python 启动GUI界面.py'")
    print("3. 运行 'python gui_launcher.py'")
    
    print("\n💡 使用提示:")
    print("• 首次使用建议先创建一个测试实例")
    print("• 可以使用批量配置快速创建多个实例")
    print("• 双击实例卡片是最快的启动方式")
    print("• 右键点击实例可以进行管理操作")
    
    print("\n🔧 技术特性:")
    print("• 基于tkinter - 无需额外依赖")
    print("• 多线程设计 - 界面响应流畅")
    print("• 进程监控 - 实时检测浏览器状态")
    print("• 模块化架构 - 易于扩展和维护")
    
    print("\n" + "=" * 60)
    
    # 询问是否启动GUI
    try:
        choice = input("是否现在启动GUI界面？(y/n): ").lower().strip()
        if choice in ['y', 'yes', '是', '']:
            print("\n正在启动GUI界面...")
            launch_gui()
        else:
            print("演示结束。")
    except KeyboardInterrupt:
        print("\n\n演示结束。")

def launch_gui():
    """启动GUI界面"""
    try:
        from gui_launcher import main
        main()
    except ImportError as e:
        print(f"❌ 导入GUI模块失败: {e}")
        print("请确保gui_launcher.py文件存在且完整")
    except Exception as e:
        print(f"❌ 启动GUI失败: {e}")

def check_dependencies():
    """检查依赖"""
    print("🔍 检查依赖库...")
    
    dependencies = [
        ("tkinter", "GUI框架"),
        ("PIL", "图像处理"),
        ("psutil", "进程监控")
    ]
    
    all_ok = True
    
    for module, desc in dependencies:
        try:
            if module == "PIL":
                from PIL import Image, ImageTk
            else:
                __import__(module)
            print(f"✅ {module} - {desc}")
        except ImportError:
            print(f"❌ {module} - {desc} (缺失)")
            all_ok = False
    
    if not all_ok:
        print("\n⚠️  部分依赖库缺失，可能影响GUI功能")
        print("建议安装: pip install pillow psutil")
    else:
        print("✅ 所有依赖库已就绪")
    
    return all_ok

def show_file_structure():
    """显示文件结构"""
    print("\n📁 GUI相关文件:")
    
    gui_files = [
        ("gui_launcher.py", "主GUI界面文件"),
        ("gui_dialogs.py", "对话框模块"),
        ("启动GUI界面.py", "Python启动器"),
        ("启动GUI界面.bat", "批处理启动器"),
        ("test_gui.py", "GUI测试脚本"),
        ("demo_gui.py", "GUI演示脚本")
    ]
    
    for filename, description in gui_files:
        if Path(filename).exists():
            print(f"✅ {filename:<20} - {description}")
        else:
            print(f"❌ {filename:<20} - {description} (缺失)")

def main():
    """主函数"""
    print("正在准备GUI演示...")
    
    # 检查文件结构
    show_file_structure()
    
    # 检查依赖
    deps_ok = check_dependencies()
    
    print("\n" + "=" * 60)
    
    if deps_ok:
        demo_gui()
    else:
        print("❌ 依赖检查失败，请先安装必要的库")
        print("安装命令: pip install pillow psutil")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 再见！")
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        input("按回车键退出...")
