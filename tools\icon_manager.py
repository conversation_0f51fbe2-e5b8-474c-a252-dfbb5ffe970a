#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图标管理工具
负责管理浏览器实例的自定义图标
"""

import os
import shutil
import subprocess
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont
import json


class IconManager:
    """图标管理器"""
    
    def __init__(self, base_dir=None):
        """初始化图标管理器"""
        if base_dir is None:
            self.base_dir = Path(__file__).parent.parent
        else:
            self.base_dir = Path(base_dir)
            
        self.icons_dir = self.base_dir / "icons"
        self.instances_dir = self.base_dir / "instances"
        
        # 确保图标目录存在
        self.icons_dir.mkdir(exist_ok=True)
        
        # 预定义的图标颜色主题
        self.color_themes = {
            "default": "#4285F4",    # Google蓝
            "work": "#1976D2",       # 深蓝色
            "personal": "#388E3C",   # 绿色
            "shopping": "#F57C00",   # 橙色
            "social": "#7B1FA2",     # 紫色
            "dev": "#D32F2F",        # 红色
            "finance": "#689F38",    # 橄榄绿
            "entertainment": "#E91E63", # 粉红色
            "education": "#5D4037",  # 棕色
            "gaming": "#FF5722"      # 深橙色
        }
    
    def list_available_icons(self):
        """列出可用的图标"""
        icons = []
        
        # 扫描图标目录
        if self.icons_dir.exists():
            for icon_file in self.icons_dir.glob("*.ico"):
                icons.append({
                    "name": icon_file.stem,
                    "file": icon_file.name,
                    "path": str(icon_file),
                    "size": icon_file.stat().st_size,
                    "type": "custom"
                })
        
        # 添加可生成的主题图标
        for theme_name, color in self.color_themes.items():
            theme_icon_path = self.icons_dir / f"{theme_name}.ico"
            icons.append({
                "name": theme_name,
                "file": f"{theme_name}.ico",
                "path": str(theme_icon_path),
                "color": color,
                "type": "theme",
                "exists": theme_icon_path.exists()
            })
        
        return icons

    def get_available_icons(self):
        """获取可用图标名称列表（不带.ico后缀）"""
        icon_names = []

        # 扫描图标目录
        if self.icons_dir.exists():
            for icon_file in self.icons_dir.glob("*.ico"):
                icon_names.append(icon_file.stem)

        # 添加主题图标名称
        for theme_name in self.color_themes.keys():
            if theme_name not in icon_names:
                icon_names.append(theme_name)

        # 排序并返回
        return sorted(icon_names)

    def generate_theme_icon(self, theme_name, size=256):
        """生成主题图标"""
        if theme_name not in self.color_themes:
            print(f"未知主题: {theme_name}")
            return False
        
        try:
            color = self.color_themes[theme_name]
            icon_path = self.icons_dir / f"{theme_name}.ico"
            
            # 创建图标图像
            img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
            draw = ImageDraw.Draw(img)
            
            # 绘制圆形背景
            margin = size // 8
            draw.ellipse([margin, margin, size-margin, size-margin], 
                        fill=color, outline=None)
            
            # 绘制Chrome样式的圆环
            ring_width = size // 16
            draw.ellipse([margin + ring_width, margin + ring_width, 
                         size - margin - ring_width, size - margin - ring_width], 
                        fill=(255, 255, 255, 200), outline=None)
            
            # 绘制内圆
            inner_margin = size // 3
            draw.ellipse([inner_margin, inner_margin, size-inner_margin, size-inner_margin], 
                        fill=color, outline=None)
            
            # 添加文字标识
            try:
                # 尝试使用系统字体
                font_size = size // 8
                font = ImageFont.truetype("arial.ttf", font_size)
            except:
                # 如果没有找到字体，使用默认字体
                font = ImageFont.load_default()
            
            # 绘制主题名称首字母
            text = theme_name[0].upper()
            bbox = draw.textbbox((0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            text_x = (size - text_width) // 2
            text_y = (size - text_height) // 2
            
            draw.text((text_x, text_y), text, fill='white', font=font)
            
            # 生成多种尺寸的图标
            sizes = [16, 32, 48, 64, 128, 256]
            icon_images = []
            
            for icon_size in sizes:
                resized_img = img.resize((icon_size, icon_size), Image.Resampling.LANCZOS)
                icon_images.append(resized_img)
            
            # 保存为ICO文件
            icon_images[0].save(icon_path, format='ICO', sizes=[(s, s) for s in sizes])
            
            print(f"已生成主题图标: {icon_path}")
            return True

        except Exception as e:
            print(f"生成图标失败: {e}")
            return False
    
    def generate_all_theme_icons(self):
        """生成所有主题图标"""
        print("生成主题图标...")
        success_count = 0

        for theme_name in self.color_themes.keys():
            if self.generate_theme_icon(theme_name):
                success_count += 1

        print(f"成功生成 {success_count}/{len(self.color_themes)} 个主题图标")
        return success_count
    
    def copy_icon_to_library(self, source_path, icon_name):
        """复制图标到图标库"""
        try:
            source_path = Path(source_path)
            if not source_path.exists():
                print(f"源图标文件不存在: {source_path}")
                return False

            # 确保文件名有.ico扩展名
            if not icon_name.endswith('.ico'):
                icon_name += '.ico'

            target_path = self.icons_dir / icon_name
            shutil.copy2(source_path, target_path)

            print(f"已复制图标到库: {target_path}")
            return True

        except Exception as e:
            print(f"复制图标失败: {e}")
            return False
    
    def set_instance_icon(self, instance_name, icon_name):
        """为实例设置图标"""
        try:
            instance_dir = self.instances_dir / instance_name
            if not instance_dir.exists():
                print(f"实例不存在: {instance_name}")
                return False
            
            # 确保图标文件存在
            if not icon_name.endswith('.ico'):
                icon_name += '.ico'
            
            icon_path = self.icons_dir / icon_name
            if not icon_path.exists():
                # 尝试生成主题图标
                theme_name = icon_name.replace('.ico', '')
                if theme_name in self.color_themes:
                    print(f"🎨 生成主题图标: {theme_name}")
                    if not self.generate_theme_icon(theme_name):
                        return False
                else:
                    print(f"图标文件不存在: {icon_path}")
                    return False
            
            # 更新实例配置
            config_path = instance_dir / "config.json"
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                config['icon'] = icon_name
                config['last_modified'] = self._get_current_time()
                
                with open(config_path, 'w', encoding='utf-8') as f:
                    json.dump(config, f, ensure_ascii=False, indent=2)
                
                print(f"已为实例 {instance_name} 设置图标: {icon_name}")
                return True
            else:
                print(f"实例配置文件不存在: {config_path}")
                return False

        except Exception as e:
            print(f"设置图标失败: {e}")
            return False
    
    def create_desktop_shortcut(self, instance_name):
        """为实例创建桌面快捷方式（Windows）"""
        try:
            instance_dir = self.instances_dir / instance_name
            config_path = instance_dir / "config.json"
            
            if not config_path.exists():
                print(f"实例配置不存在: {instance_name}")
                return False
            
            # 读取实例配置
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            display_name = config.get('display_name', instance_name)
            icon_name = config.get('icon', 'default.ico')
            
            # 快捷方式路径
            desktop = Path.home() / "Desktop"
            shortcut_path = desktop / f"{display_name}.lnk"
            
            # Python脚本路径
            script_path = instance_dir / f"chrome_{instance_name}.py"
            icon_path = self.icons_dir / icon_name
            
            # 创建快捷方式（Windows）
            if os.name == 'nt':
                try:
                    import win32com.client
                    shell = win32com.client.Dispatch("WScript.Shell")
                    shortcut = shell.CreateShortCut(str(shortcut_path))
                    shortcut.Targetpath = "python"
                    shortcut.Arguments = f'"{script_path}"'
                    shortcut.WorkingDirectory = str(script_path.parent)
                    shortcut.IconLocation = str(icon_path)
                    shortcut.Description = config.get('description', '')
                    shortcut.save()
                    
                    print(f"已创建桌面快捷方式: {shortcut_path}")
                    return True
                except ImportError:
                    print("需要安装 pywin32 来创建Windows快捷方式")
                    print("运行: pip install pywin32")
                    return False
            else:
                print("当前系统不支持自动创建快捷方式")
                return False

        except Exception as e:
            print(f"创建快捷方式失败: {e}")
            return False
    
    def _get_current_time(self):
        """获取当前时间"""
        from datetime import datetime
        return datetime.now().isoformat()


def main():
    """命令行接口"""
    import argparse
    
    parser = argparse.ArgumentParser(description="图标管理工具")
    subparsers = parser.add_subparsers(dest="command", help="可用命令")
    
    # 列出图标命令
    list_parser = subparsers.add_parser("list", help="列出可用图标")
    
    # 生成主题图标命令
    generate_parser = subparsers.add_parser("generate", help="生成主题图标")
    generate_parser.add_argument("--theme", help="指定主题名称")
    generate_parser.add_argument("--all", action="store_true", help="生成所有主题图标")
    
    # 设置实例图标命令
    set_parser = subparsers.add_parser("set", help="为实例设置图标")
    set_parser.add_argument("instance", help="实例名称")
    set_parser.add_argument("icon", help="图标名称")
    
    # 创建快捷方式命令
    shortcut_parser = subparsers.add_parser("shortcut", help="创建桌面快捷方式")
    shortcut_parser.add_argument("instance", help="实例名称")
    
    # 复制图标命令
    copy_parser = subparsers.add_parser("copy", help="复制图标到库")
    copy_parser.add_argument("source", help="源图标路径")
    copy_parser.add_argument("name", help="图标名称")
    
    args = parser.parse_args()
    
    manager = IconManager()
    
    if args.command == "list":
        icons = manager.list_available_icons()
        print("可用图标:")
        for icon in icons:
            status = "[存在]" if icon.get("exists", True) else "[缺失]"
            icon_type = icon["type"]
            if icon_type == "theme":
                color = icon.get("color", "")
                print(f"  {status} {icon['name']} ({icon_type}) - {color}")
            else:
                size_kb = icon["size"] // 1024
                print(f"  {status} {icon['name']} ({icon_type}) - {size_kb}KB")
    
    elif args.command == "generate":
        if args.all:
            manager.generate_all_theme_icons()
        elif args.theme:
            manager.generate_theme_icon(args.theme)
        else:
            print("请指定 --theme 主题名称 或 --all 生成所有主题")
    
    elif args.command == "set":
        manager.set_instance_icon(args.instance, args.icon)
    
    elif args.command == "shortcut":
        manager.create_desktop_shortcut(args.instance)
    
    elif args.command == "copy":
        manager.copy_icon_to_library(args.source, args.name)
    
    else:
        parser.print_help()


if __name__ == "__main__":
    main()
