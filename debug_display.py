#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试显示问题
"""

import sys
import os
import json
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))
sys.path.append(str(Path(__file__).parent / "tools"))

def check_instance_data():
    """检查实例数据结构"""
    print("🔍 检查实例数据结构...")
    
    try:
        from browser_manager import BrowserManager
        
        manager = BrowserManager()
        instances = manager.list_instances()
        
        print(f"✅ 获取到 {len(instances)} 个实例")
        
        for i, instance in enumerate(instances):
            print(f"\n📋 实例 {i+1}: {instance['name']}")
            print(f"   路径: {instance['path']}")
            
            config = instance.get('config', {})
            print(f"   配置结构:")
            print(f"     - display_name: {config.get('display_name', '未设置')}")
            print(f"     - description: {config.get('description', '未设置')}")
            print(f"     - icon: {config.get('icon', '未设置')}")
            
            # 检查图标文件是否存在
            icon_name = config.get('icon', 'default')
            icon_path = Path(__file__).parent / "icons" / f"{icon_name}.ico"
            icon_exists = icon_path.exists()
            print(f"     - 图标文件存在: {icon_exists} ({icon_path})")
            
            if not icon_exists:
                # 检查默认图标
                default_icon_path = Path(__file__).parent / "icons" / "default.ico"
                default_exists = default_icon_path.exists()
                print(f"     - 默认图标存在: {default_exists} ({default_icon_path})")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查实例数据失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_icon_files():
    """检查图标文件"""
    print("\n🔍 检查图标文件...")
    
    try:
        icons_dir = Path(__file__).parent / "icons"
        
        if not icons_dir.exists():
            print(f"❌ 图标目录不存在: {icons_dir}")
            return False
        
        print(f"✅ 图标目录存在: {icons_dir}")
        
        # 列出所有图标文件
        icon_files = list(icons_dir.glob("*.ico"))
        print(f"📁 找到 {len(icon_files)} 个图标文件:")
        
        for icon_file in icon_files:
            file_size = icon_file.stat().st_size
            print(f"   - {icon_file.name} ({file_size} bytes)")
        
        # 检查默认图标
        default_icon = icons_dir / "default.ico"
        if default_icon.exists():
            print(f"✅ 默认图标存在: {default_icon}")
        else:
            print(f"❌ 默认图标不存在: {default_icon}")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查图标文件失败: {e}")
        return False

def test_gui_display():
    """测试GUI显示"""
    print("\n🔍 测试GUI显示...")
    
    try:
        import tkinter as tk
        from PIL import Image, ImageTk
        from browser_manager import BrowserManager
        
        # 获取测试数据
        manager = BrowserManager()
        instances = manager.list_instances()
        
        if not instances:
            print("❌ 没有实例可供测试")
            return False
        
        test_instance = instances[0]
        print(f"📋 测试实例: {test_instance['name']}")
        
        # 创建测试窗口
        root = tk.Tk()
        root.title("显示测试")
        root.geometry("400x200")
        
        # 测试显示名称
        config = test_instance.get('config', {})
        display_name = config.get('display_name', test_instance['name'])
        description = config.get('description', '无描述')
        icon_name = config.get('icon', 'default')
        
        print(f"   显示名称: {display_name}")
        print(f"   描述: {description}")
        print(f"   图标: {icon_name}")
        
        # 创建标签测试
        name_label = tk.Label(root, text=f"显示名称: {display_name}", font=('Arial', 12, 'bold'))
        name_label.pack(pady=10)
        
        desc_label = tk.Label(root, text=f"描述: {description}", font=('Arial', 10))
        desc_label.pack(pady=5)
        
        # 测试图标加载
        try:
            icon_path = Path(__file__).parent / "icons" / f"{icon_name}.ico"
            if not icon_path.exists():
                icon_path = Path(__file__).parent / "icons" / "default.ico"
            
            if icon_path.exists():
                img = Image.open(icon_path)
                img = img.resize((48, 48), Image.Resampling.LANCZOS)
                icon_photo = ImageTk.PhotoImage(img)
                
                icon_label = tk.Label(root, image=icon_photo)
                icon_label.pack(pady=10)
                
                print(f"✅ 图标加载成功: {icon_path}")
            else:
                icon_label = tk.Label(root, text='🌐', font=('Arial', 24))
                icon_label.pack(pady=10)
                print(f"⚠️ 使用默认文字图标")
                
        except Exception as e:
            print(f"❌ 图标加载失败: {e}")
            icon_label = tk.Label(root, text='❌', font=('Arial', 24))
            icon_label.pack(pady=10)
        
        # 显示窗口3秒后关闭
        def close_window():
            root.destroy()
        
        root.after(3000, close_window)
        
        print("✅ 测试窗口已显示，3秒后自动关闭")
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ GUI显示测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_config_files():
    """检查配置文件内容"""
    print("\n🔍 检查配置文件内容...")
    
    try:
        instances_dir = Path(__file__).parent / "instances"
        
        if not instances_dir.exists():
            print(f"❌ 实例目录不存在: {instances_dir}")
            return False
        
        for instance_dir in instances_dir.iterdir():
            if instance_dir.is_dir():
                config_path = instance_dir / "config.json"
                if config_path.exists():
                    print(f"\n📁 实例: {instance_dir.name}")
                    
                    try:
                        with open(config_path, 'r', encoding='utf-8') as f:
                            config = json.load(f)
                        
                        print(f"   配置内容:")
                        for key, value in config.items():
                            if isinstance(value, dict):
                                print(f"     {key}:")
                                for sub_key, sub_value in value.items():
                                    print(f"       {sub_key}: {sub_value}")
                            else:
                                print(f"     {key}: {value}")
                                
                    except Exception as e:
                        print(f"   ❌ 读取配置失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查配置文件失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🔍 显示问题调试工具")
    print("=" * 60)
    
    tests = [
        ("实例数据结构", check_instance_data),
        ("图标文件", check_icon_files),
        ("配置文件内容", check_config_files),
        ("GUI显示测试", test_gui_display)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*15} {test_name} {'='*15}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 60)
    print("📊 调试结果")
    print("=" * 60)
    
    if passed == total:
        print("🎉 所有检查通过！")
        print("\n💡 如果显示仍有问题，可能是:")
        print("1. GUI刷新机制问题")
        print("2. 事件绑定问题")
        print("3. 缓存问题")
    else:
        print(f"⚠️ {total - passed}/{total} 个检查失败")
        print("需要修复相关问题")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"\n调试异常: {e}")
    
    print("\n按回车键退出...")
    input()
