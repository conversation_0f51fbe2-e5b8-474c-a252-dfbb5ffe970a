#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器实例全局启动器
可以选择启动任意实例
生成时间: 2025-07-26 21:49:21
"""

import os
import sys
import json
import subprocess
from pathlib import Path


def get_available_instances():
    """获取可用的实例列表"""
    script_dir = Path(__file__).parent
    instances_dir = script_dir.parent / "instances"
    
    instances = []
    if instances_dir.exists():
        for item in instances_dir.iterdir():
            if item.is_dir():
                config_path = item / "config.json"
                if config_path.exists():
                    try:
                        with open(config_path, 'r', encoding='utf-8') as f:
                            config = json.load(f)
                        instances.append({
                            "name": item.name,
                            "display_name": config.get("display_name", item.name),
                            "description": config.get("description", ""),
                            "icon": config.get("icon", "default.ico")
                        })
                    except Exception:
                        pass
    
    return instances


def launch_instance(instance_name):
    """启动指定实例"""
    script_dir = Path(__file__).parent
    instance_dir = script_dir.parent / "instances" / instance_name
    launcher_script = instance_dir / f"chrome_{instance_name}.py"
    
    if not launcher_script.exists():
        print(f"启动脚本不存在: {launcher_script}")
        return False

    try:
        # 启动实例
        subprocess.Popen([sys.executable, str(launcher_script)],
                        cwd=str(instance_dir))
        return True
    except Exception as e:
        print(f"启动失败: {e}")
        return False


def main():
    """主函数"""
    print("🌟 浏览器实例启动器")
    print("=" * 50)
    
    instances = get_available_instances()
    
    if not instances:
        print("没有找到可用的实例")
        print("请先使用 browser_manager.py 创建实例")
        input("按回车键退出...")
        return

    print("可用实例:")
    for i, instance in enumerate(instances, 1):
        print(f"  {i}. {instance['display_name']}")
        if instance['description']:
            print(f"     {instance['description']}")
        print(f"     图标: {instance['icon']}")
        print()
    
    # 用户选择
    while True:
        try:
            choice = input("请选择要启动的实例 (输入数字，0退出): ").strip()
            
            if choice == "0":
                print("再见！")
                break

            choice_num = int(choice)
            if 1 <= choice_num <= len(instances):
                selected_instance = instances[choice_num - 1]
                print(f"启动 {selected_instance['display_name']}...")

                if launch_instance(selected_instance['name']):
                    print(f"{selected_instance['display_name']} 启动成功")
                    break
                else:
                    print("启动失败")
            else:
                print("无效选择，请重新输入")

        except ValueError:
            print("请输入有效数字")
        except KeyboardInterrupt:
            print("\n再见！")
            break


if __name__ == "__main__":
    main()
