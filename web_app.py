#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
现代化Web界面 - Flask应用主文件
"""

import sys
import os
import json
from pathlib import Path
from flask import Flask, render_template, request, jsonify, send_from_directory
from datetime import datetime
import threading
import webbrowser
import time

# 添加tools目录到Python路径
sys.path.append(str(Path(__file__).parent / "tools"))

from browser_manager import BrowserManager
from icon_manager import IconManager

app = Flask(__name__)
app.secret_key = 'browser_manager_secret_key_2024'

# 初始化管理器
browser_manager = BrowserManager()
icon_manager = IconManager()

@app.route('/')
def index():
    """主页面"""
    return render_template('index.html')

@app.route('/api/instances')
def api_instances():
    """获取浏览器实例列表"""
    try:
        instances = browser_manager.list_instances()
        
        # 增强实例数据
        enhanced_instances = []
        for i, instance in enumerate(instances, 1):
            config = instance.get('config', {})
            enhanced_instance = {
                'id': i,
                'name': instance['name'],
                'display_name': config.get('display_name', instance['name']),
                'description': config.get('description', ''),
                'icon': config.get('icon', 'default'),
                'group': config.get('group', '默认分组'),
                'last_launch': config.get('last_launch', '未启动'),
                'status': 'running' if browser_manager.is_instance_running(instance['name']) else 'stopped',
                'path': instance['path']
            }
            enhanced_instances.append(enhanced_instance)
        
        return jsonify({
            'success': True,
            'data': enhanced_instances,
            'total': len(enhanced_instances)
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/instances', methods=['POST'])
def api_create_instance():
    """创建新实例"""
    try:
        data = request.get_json()
        
        name = data.get('name', '').strip()
        if not name:
            return jsonify({
                'success': False,
                'error': '实例名称不能为空'
            }), 400
        
        # 检查名称是否已存在
        instances = browser_manager.list_instances()
        existing_names = [inst['name'] for inst in instances]
        if name in existing_names:
            return jsonify({
                'success': False,
                'error': '实例名称已存在'
            }), 400
        
        # 创建实例配置
        config = {
            'display_name': data.get('display_name', f'Chrome - {name}'),
            'description': data.get('description', ''),
            'icon': data.get('icon', 'default'),
            'group': data.get('group', '默认分组'),
            'created_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'last_launch': '未启动'
        }
        
        # 创建实例
        if browser_manager.create_instance(name, config):
            return jsonify({
                'success': True,
                'message': f'实例 "{name}" 创建成功'
            })
        else:
            return jsonify({
                'success': False,
                'error': '创建实例失败'
            }), 500
            
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/instances/<instance_name>', methods=['PUT'])
def api_update_instance(instance_name):
    """更新实例"""
    try:
        data = request.get_json()
        
        # 获取现有配置
        instances = browser_manager.list_instances()
        instance = None
        for inst in instances:
            if inst['name'] == instance_name:
                instance = inst
                break
        
        if not instance:
            return jsonify({
                'success': False,
                'error': '实例不存在'
            }), 404
        
        # 更新配置
        config = instance.get('config', {})
        config.update({
            'display_name': data.get('display_name', config.get('display_name')),
            'description': data.get('description', config.get('description')),
            'icon': data.get('icon', config.get('icon')),
            'group': data.get('group', config.get('group')),
            'updated_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        })
        
        # 保存更新
        if browser_manager.update_instance(instance_name, config):
            return jsonify({
                'success': True,
                'message': f'实例 "{instance_name}" 更新成功'
            })
        else:
            return jsonify({
                'success': False,
                'error': '更新实例失败'
            }), 500
            
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/instances/<instance_name>', methods=['DELETE'])
def api_delete_instance(instance_name):
    """删除实例"""
    try:
        if browser_manager.delete_instance(instance_name):
            return jsonify({
                'success': True,
                'message': f'实例 "{instance_name}" 删除成功'
            })
        else:
            return jsonify({
                'success': False,
                'error': '删除实例失败'
            }), 500
            
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/instances/<instance_name>/launch', methods=['POST'])
def api_launch_instance(instance_name):
    """启动实例"""
    try:
        if browser_manager.launch_instance(instance_name):
            # 更新最后启动时间
            instances = browser_manager.list_instances()
            for inst in instances:
                if inst['name'] == instance_name:
                    config = inst.get('config', {})
                    config['last_launch'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    browser_manager.update_instance(instance_name, config)
                    break
            
            return jsonify({
                'success': True,
                'message': f'实例 "{instance_name}" 启动成功'
            })
        else:
            return jsonify({
                'success': False,
                'error': '启动实例失败'
            }), 500
            
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/instances/<instance_name>/stop', methods=['POST'])
def api_stop_instance(instance_name):
    """停止实例"""
    try:
        if browser_manager.stop_instance(instance_name):
            return jsonify({
                'success': True,
                'message': f'实例 "{instance_name}" 停止成功'
            })
        else:
            return jsonify({
                'success': False,
                'error': '停止实例失败'
            }), 500
            
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/icons')
def api_icons():
    """获取可用图标列表"""
    try:
        icons = icon_manager.get_available_icons()
        return jsonify({
            'success': True,
            'data': icons
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/groups')
def api_groups():
    """获取分组列表"""
    try:
        instances = browser_manager.list_instances()
        groups = set()
        for instance in instances:
            config = instance.get('config', {})
            group = config.get('group', '默认分组')
            groups.add(group)
        
        groups_list = list(groups) if groups else ['默认分组']
        
        return jsonify({
            'success': True,
            'data': groups_list
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/static/<path:filename>')
def static_files(filename):
    """静态文件服务"""
    return send_from_directory('static', filename)

def open_browser():
    """延迟打开浏览器"""
    time.sleep(1.5)
    webbrowser.open('http://127.0.0.1:5000')

def main():
    """主函数"""
    print("🚀 启动现代化浏览器管理界面...")
    print("📍 访问地址: http://127.0.0.1:5000")
    
    # 在新线程中打开浏览器
    threading.Thread(target=open_browser, daemon=True).start()
    
    # 启动Flask应用
    app.run(
        host='127.0.0.1',
        port=5000,
        debug=False,
        use_reloader=False
    )

if __name__ == '__main__':
    main()
