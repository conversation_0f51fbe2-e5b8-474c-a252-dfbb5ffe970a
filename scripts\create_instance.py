#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建浏览器实例的交互式脚本
"""

import sys
import os
from pathlib import Path

# 添加tools目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "tools"))

from browser_manager import BrowserManager


def get_user_input():
    """获取用户输入"""
    print("🌟 欢迎使用浏览器实例创建工具")
    print("=" * 50)
    
    # 获取实例名称
    while True:
        instance_name = input("请输入实例名称（英文字母、数字、下划线）: ").strip()
        if instance_name and instance_name.replace("_", "").replace("-", "").isalnum():
            break
        print("❌ 实例名称只能包含英文字母、数字、下划线和连字符")
    
    # 获取显示名称
    display_name = input(f"请输入显示名称（默认: Chrome - {instance_name}）: ").strip()
    if not display_name:
        display_name = f"Chrome - {instance_name}"
    
    # 获取描述
    description = input("请输入描述（可选）: ").strip()
    
    # 获取主页
    homepage = input("请输入主页URL（可选）: ").strip()
    
    # 获取图标
    print("\n可用图标:")
    print("  • default.ico - 默认Chrome图标")
    print("  • work.ico - 工作图标（如果存在）")
    print("  • personal.ico - 个人图标（如果存在）")
    print("  • shopping.ico - 购物图标（如果存在）")
    print("  • social.ico - 社交图标（如果存在）")
    
    icon = input("请选择图标文件名（默认: default.ico）: ").strip()
    if not icon:
        icon = "default.ico"
    
    return {
        "instance_name": instance_name,
        "display_name": display_name,
        "description": description,
        "homepage": homepage,
        "icon": icon
    }


def main():
    """主函数"""
    try:
        # 获取用户输入
        config = get_user_input()
        
        print("\n📋 配置信息:")
        print(f"  实例名称: {config['instance_name']}")
        print(f"  显示名称: {config['display_name']}")
        print(f"  描述: {config['description'] or '无'}")
        print(f"  主页: {config['homepage'] or '默认'}")
        print(f"  图标: {config['icon']}")
        
        # 确认创建
        confirm = input("\n确认创建实例？(y/N): ").strip().lower()
        if confirm not in ['y', 'yes']:
            print("❌ 已取消创建")
            return
        
        # 创建实例
        print("\n🔄 正在创建实例...")
        manager = BrowserManager()
        
        success = manager.create_instance(
            instance_name=config['instance_name'],
            display_name=config['display_name'],
            description=config['description'],
            homepage=config['homepage'],
            icon=config['icon']
        )
        
        if success:
            print(f"\n🎉 实例创建成功！")
            print(f"📁 实例目录: instances/{config['instance_name']}")
            print(f"🚀 启动脚本: instances/{config['instance_name']}/chrome_{config['instance_name']}.py")
            print(f"\n💡 使用方法:")
            print(f"   cd instances/{config['instance_name']}")
            print(f"   python chrome_{config['instance_name']}.py")
        else:
            print("\n❌ 实例创建失败")
            
    except KeyboardInterrupt:
        print("\n\n❌ 用户取消操作")
    except Exception as e:
        print(f"\n❌ 发生错误: {e}")


if __name__ == "__main__":
    main()
