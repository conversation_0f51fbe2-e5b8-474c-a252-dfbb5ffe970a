#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
from pathlib import Path

# 添加tools目录到Python路径
sys.path.append(str(Path(__file__).parent / "tools"))

from browser_manager import BrowserManager

def test_launch_instance():
    """直接测试启动实例功能"""
    print("=" * 60)
    print("🚀 直接测试启动实例功能")
    print("=" * 60)
    
    # 创建BrowserManager实例
    manager = BrowserManager()
    
    # 列出所有实例
    print("📋 列出所有实例:")
    instances = manager.list_instances()
    for i, instance in enumerate(instances, 1):
        print(f"  {i}. {instance['name']} - {instance['config'].get('display_name', '无名称')}")
    
    if not instances:
        print("❌ 没有找到任何实例")
        return
    
    # 选择第一个实例进行测试
    test_instance = instances[0]
    instance_name = test_instance['name']
    
    print(f"\n🎯 测试启动实例: {instance_name}")
    
    # 1. 测试Chrome路径检测
    print("\n1️⃣ 测试Chrome路径检测:")
    chrome_path = manager._find_chrome_executable()
    if chrome_path:
        print(f"✅ 找到Chrome: {chrome_path}")
        if Path(chrome_path).exists():
            print(f"✅ 文件存在")
        else:
            print(f"❌ 文件不存在")
    else:
        print("❌ 未找到Chrome")
        return
    
    # 2. 检查实例目录
    print(f"\n2️⃣ 检查实例目录:")
    instance_dir = Path("instances") / instance_name
    print(f"📁 实例目录: {instance_dir}")
    if instance_dir.exists():
        print("✅ 实例目录存在")
        
        # 检查Data目录
        data_dir = instance_dir / "Data"
        if data_dir.exists():
            print("✅ Data目录存在")
        else:
            print("❌ Data目录不存在")
            
        # 检查配置文件
        config_file = instance_dir / "config.json"
        if config_file.exists():
            print("✅ 配置文件存在")
        else:
            print("❌ 配置文件不存在")
    else:
        print("❌ 实例目录不存在")
        return
    
    # 3. 测试启动
    print(f"\n3️⃣ 测试启动实例:")
    try:
        result = manager.launch_instance(instance_name)
        if result:
            print("✅ 启动成功")
        else:
            print("❌ 启动失败")
    except Exception as e:
        print(f"❌ 启动异常: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("🏁 测试完成")
    print("=" * 60)

if __name__ == "__main__":
    test_launch_instance()
