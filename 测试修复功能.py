#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复的功能
"""

import sys
import os
import json
import time
from pathlib import Path

# 添加tools目录到路径
sys.path.append(str(Path(__file__).parent / "tools"))

def test_launch_instance():
    """测试启动实例功能"""
    print("🚀 测试启动实例功能...")
    
    try:
        from browser_manager import BrowserManager
        manager = BrowserManager()
        
        # 获取实例列表
        instances = manager.list_instances()
        if not instances:
            print("❌ 没有可用的实例进行测试")
            return False
        
        # 选择第一个实例进行测试
        test_instance = instances[0]
        instance_name = test_instance['name']
        
        print(f"📝 测试实例: {instance_name}")
        
        # 检查launch_instance方法是否存在
        if hasattr(manager, 'launch_instance'):
            print("✅ launch_instance 方法存在")
            
            # 检查实例当前状态
            is_running = manager.is_instance_running(instance_name)
            print(f"📊 当前状态: {'运行中' if is_running else '已停止'}")
            
            if is_running:
                print("⚠️ 实例已在运行，先停止再测试启动")
                if manager.stop_instance(instance_name):
                    print("✅ 实例停止成功")
                    time.sleep(2)
                else:
                    print("❌ 实例停止失败")
                    return False
            
            # 测试启动
            print(f"🚀 尝试启动实例: {instance_name}")
            success = manager.launch_instance(instance_name)
            
            if success:
                print("✅ 启动实例功能正常")
                return True
            else:
                print("❌ 启动实例失败")
                return False
        else:
            print("❌ launch_instance 方法不存在")
            return False
            
    except Exception as e:
        print(f"❌ 测试启动功能失败: {e}")
        return False

def test_copy_instance():
    """测试复制实例功能"""
    print("\n📋 测试复制实例功能...")
    
    try:
        from browser_manager import BrowserManager
        manager = BrowserManager()
        
        # 获取实例列表
        instances = manager.list_instances()
        if not instances:
            print("❌ 没有可用的实例进行测试")
            return False
        
        # 选择第一个实例进行复制测试
        source_instance = instances[0]
        source_name = source_instance['name']
        target_name = f"测试复制_{int(time.time())}"
        
        print(f"📝 源实例: {source_name}")
        print(f"📝 目标实例: {target_name}")
        
        # 检查copy_instance方法是否存在
        if hasattr(manager, 'copy_instance'):
            print("✅ copy_instance 方法存在")
            
            # 测试复制
            print(f"📋 尝试复制实例: {source_name} -> {target_name}")
            success = manager.copy_instance(source_name, target_name)
            
            if success:
                print("✅ 复制实例功能正常")
                
                # 验证复制结果
                new_instances = manager.list_instances()
                copied_instance = next((inst for inst in new_instances if inst['name'] == target_name), None)
                
                if copied_instance:
                    print("✅ 复制的实例存在")
                    
                    # 清理测试实例
                    print(f"🗑️ 清理测试实例: {target_name}")
                    if manager.delete_instance(target_name):
                        print("✅ 测试实例清理成功")
                    else:
                        print("⚠️ 测试实例清理失败")
                    
                    return True
                else:
                    print("❌ 复制的实例不存在")
                    return False
            else:
                print("❌ 复制实例失败")
                return False
        else:
            print("❌ copy_instance 方法不存在")
            return False
            
    except Exception as e:
        print(f"❌ 测试复制功能失败: {e}")
        return False

def test_api_endpoints():
    """测试API端点"""
    print("\n🔌 测试API端点...")
    
    try:
        import urllib.request
        import urllib.parse
        
        base_url = "http://127.0.0.1:8000"
        
        # 测试获取实例列表
        try:
            response = urllib.request.urlopen(f"{base_url}/api/instances", timeout=5)
            if response.getcode() == 200:
                data = json.loads(response.read().decode('utf-8'))
                if data.get('success'):
                    print(f"✅ 获取实例列表API正常 (找到 {len(data.get('data', []))} 个实例)")
                else:
                    print("❌ 获取实例列表API返回错误")
                    return False
            else:
                print(f"❌ 获取实例列表API HTTP错误: {response.getcode()}")
                return False
        except Exception as e:
            print(f"❌ 获取实例列表API失败: {e}")
            return False
        
        # 测试启动实例API（如果有实例的话）
        instances = data.get('data', [])
        if instances:
            test_instance = instances[0]['name']
            try:
                # 构建POST请求
                req = urllib.request.Request(
                    f"{base_url}/api/instances/{test_instance}/launch",
                    method='POST'
                )
                response = urllib.request.urlopen(req, timeout=10)
                
                if response.getcode() == 200:
                    result = json.loads(response.read().decode('utf-8'))
                    if result.get('success'):
                        print(f"✅ 启动实例API正常")
                    else:
                        print(f"⚠️ 启动实例API返回: {result.get('error', '未知错误')}")
                else:
                    print(f"❌ 启动实例API HTTP错误: {response.getcode()}")
            except Exception as e:
                print(f"⚠️ 启动实例API测试失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试API端点失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🔧 修复功能测试")
    print("=" * 60)
    
    tests = [
        ("启动实例功能", test_launch_instance),
        ("复制实例功能", test_copy_instance),
        ("API端点", test_api_endpoints)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 统计结果
    print("\n" + "=" * 60)
    print("📊 测试结果:")
    print("=" * 60)
    
    total = len(results)
    passed = sum(1 for _, success in results if success)
    failed = total - passed
    
    print(f"总测试: {total}")
    print(f"通过: {passed}")
    print(f"失败: {failed}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if failed > 0:
        print("\n❌ 失败的测试:")
        for test_name, success in results:
            if not success:
                print(f"  - {test_name}")
    
    print("\n" + "=" * 60)
    if failed == 0:
        print("🎉 所有修复功能测试通过！")
        print("\n✅ 修复状态:")
        print("✅ 启动实例功能已修复")
        print("✅ 复制实例功能已修复")
        print("✅ API端点正常工作")
        print("\n🌐 现代化界面可以正常使用:")
        print("   http://127.0.0.1:8000")
    else:
        print("⚠️ 部分功能仍有问题，需要进一步修复")
    print("=" * 60)
    
    return failed == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
