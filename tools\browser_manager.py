#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器实例管理器
负责创建、删除、配置和管理浏览器实例
"""

import os
import sys
import json
import shutil
import subprocess
import psutil
import time
from datetime import datetime
from pathlib import Path


class BrowserManager:
    """浏览器实例管理器"""
    
    def __init__(self, base_dir=None):
        """初始化管理器
        
        Args:
            base_dir: 项目根目录，默认为脚本所在目录的上级目录
        """
        if base_dir is None:
            self.base_dir = Path(__file__).parent.parent
        else:
            self.base_dir = Path(base_dir)
            
        self.chrome_portable_dir = self.base_dir / "GoogleChromePortable"
        self.instances_dir = self.base_dir / "instances"
        self.icons_dir = self.base_dir / "icons"
        self.scripts_dir = self.base_dir / "scripts"
        self.config_templates_dir = self.base_dir / "tools" / "config_templates"
        
        # 确保必要目录存在
        self._ensure_directories()
    
    def _ensure_directories(self):
        """确保必要的目录存在"""
        for directory in [self.instances_dir, self.icons_dir, self.scripts_dir]:
            directory.mkdir(exist_ok=True)
    
    def _load_config_template(self):
        """加载配置模板"""
        template_path = self.config_templates_dir / "instance_config.json"
        if template_path.exists():
            with open(template_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            # 默认配置模板
            return {
                "instance_name": "",
                "display_name": "",
                "icon": "default.ico",
                "description": "",
                "chrome_args": [
                    "--no-first-run",
                    "--no-default-browser-check",
                    "--disable-default-apps",
                    "--disable-background-mode"
                ],
                "window_settings": {
                    "width": 1200,
                    "height": 800,
                    "position": "center"
                },
                "startup_options": {
                    "homepage": "",
                    "restore_session": True,
                    "incognito": False
                },
                "created_date": "",
                "last_modified": ""
            }
    
    def create_instance(self, instance_name, display_name=None, icon="default.ico", 
                       description="", homepage="", **kwargs):
        """创建新的浏览器实例
        
        Args:
            instance_name: 实例名称（用作目录名）
            display_name: 显示名称
            icon: 图标文件名
            description: 描述
            homepage: 主页URL
            **kwargs: 其他配置参数
            
        Returns:
            bool: 创建是否成功
        """
        try:
            # 检查Chrome Portable是否存在
            if not self.chrome_portable_dir.exists():
                print(f"错误：找不到Chrome Portable目录: {self.chrome_portable_dir}")
                return False
            
            # 检查实例是否已存在
            instance_dir = self.instances_dir / instance_name
            if instance_dir.exists():
                print(f"错误：实例 '{instance_name}' 已存在")
                return False
            
            # 创建实例目录
            instance_dir.mkdir(parents=True)
            
            # 创建独立的Data目录
            data_dir = instance_dir / "Data"
            data_dir.mkdir()
            
            # 复制默认配置到实例Data目录
            default_data = self.chrome_portable_dir / "App" / "DefaultData"
            if default_data.exists():
                shutil.copytree(default_data, data_dir, dirs_exist_ok=True)
            
            # 创建实例配置文件
            config = self._load_config_template()
            config.update({
                "instance_name": instance_name,
                "display_name": display_name or f"Chrome - {instance_name}",
                "icon": icon,
                "description": description,
                "created_date": datetime.now().isoformat(),
                "last_modified": datetime.now().isoformat()
            })
            
            # 更新主页设置
            if homepage:
                config["startup_options"]["homepage"] = homepage
            
            # 应用其他配置参数
            config.update(kwargs)
            
            # 保存配置文件
            config_path = instance_dir / "config.json"
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            
            # 生成启动脚本
            self._create_launch_script(instance_name, config)
            
            print(f"成功创建浏览器实例: {instance_name}")
            return True

        except Exception as e:
            print(f"创建实例失败: {e}")
            # 清理可能创建的文件
            if instance_dir.exists():
                shutil.rmtree(instance_dir, ignore_errors=True)
            return False
    
    def _create_launch_script(self, instance_name, config):
        """为实例创建启动脚本
        
        Args:
            instance_name: 实例名称
            config: 实例配置
        """
        # 生成启动脚本内容
        script_content = self._generate_launch_script_content(instance_name, config)
        
        # 保存启动脚本
        script_path = self.instances_dir / instance_name / f"chrome_{instance_name}.py"
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        print(f"已创建启动脚本: {script_path}")
    
    def _generate_launch_script_content(self, instance_name, config):
        """生成启动脚本内容"""
        chrome_args = config.get("chrome_args", [])
        homepage = config.get("startup_options", {}).get("homepage", "")
        display_name = config.get('display_name', instance_name)

        # 构建完整的参数列表
        all_args = chrome_args.copy()
        if homepage:
            all_args.append(homepage)

        # 生成参数字符串
        args_lines = []
        for arg in all_args:
            args_lines.append(f'        "{arg}",')
        args_str = "\n".join(args_lines)

        # 使用字符串拼接而不是f-string来避免大括号冲突
        script_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
''' + display_name + ''' 启动脚本
自动生成，请勿手动修改
"""

import os
import subprocess
from pathlib import Path

def main():
    # 获取脚本所在目录
    script_dir = Path(__file__).parent
    base_dir = script_dir.parent.parent

    # Chrome Portable路径
    chrome_exe = base_dir / "GoogleChromePortable" / "App" / "Chrome-bin" / "chrome.exe"

    # 用户数据目录
    user_data_dir = script_dir / "Data" / "profile"

    # 构建启动命令
    cmd = [
        str(chrome_exe),
        f"--user-data-dir={user_data_dir}",
''' + args_str + '''
    ]

    # 启动Chrome
    try:
        subprocess.Popen(cmd)
        print(f"已启动 ''' + display_name + '''")
    except Exception as e:
        print(f"启动失败: {e}")

if __name__ == "__main__":
    main()
'''

        return script_content
    
    def list_instances(self):
        """列出所有实例"""
        if not self.instances_dir.exists():
            return []
        
        instances = []
        for item in self.instances_dir.iterdir():
            if item.is_dir():
                config_path = item / "config.json"
                if config_path.exists():
                    try:
                        with open(config_path, 'r', encoding='utf-8') as f:
                            config = json.load(f)
                        instances.append({
                            "name": item.name,
                            "config": config,
                            "path": str(item)
                        })
                    except Exception as e:
                        print(f"⚠️ 读取配置失败 {item.name}: {e}")
        
        return instances

    def update_instance(self, instance_name, new_config):
        """更新实例配置

        Args:
            instance_name: 实例名称
            new_config: 新的配置信息

        Returns:
            bool: 更新是否成功
        """
        try:
            instance_dir = self.instances_dir / instance_name
            if not instance_dir.exists():
                print(f"❌ 实例不存在: {instance_name}")
                return False

            config_path = instance_dir / "config.json"

            # 更新修改时间
            new_config['last_modified'] = datetime.now().isoformat()

            # 保存配置
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(new_config, f, indent=2, ensure_ascii=False)

            print(f"✅ 实例配置更新成功: {instance_name}")
            return True

        except Exception as e:
            print(f"❌ 更新实例配置失败: {e}")
            return False

    def copy_instance(self, source_name, target_name, new_config=None):
        """复制实例

        Args:
            source_name: 源实例名称
            target_name: 目标实例名称
            new_config: 新实例的配置信息（可选）

        Returns:
            bool: 复制是否成功
        """
        try:
            source_dir = self.instances_dir / source_name
            target_dir = self.instances_dir / target_name

            if not source_dir.exists():
                print(f"❌ 源实例不存在: {source_name}")
                return False

            if target_dir.exists():
                print(f"❌ 目标实例已存在: {target_name}")
                return False

            # 创建目标目录
            target_dir.mkdir(parents=True, exist_ok=True)

            # 复制配置文件
            source_config_path = source_dir / "config.json"
            target_config_path = target_dir / "config.json"

            if source_config_path.exists():
                with open(source_config_path, 'r', encoding='utf-8') as f:
                    source_config = json.load(f)

                # 使用新配置或复制源配置
                if new_config:
                    target_config = new_config.copy()
                    # 保留一些源配置的设置
                    target_config['chrome_args'] = source_config.get('chrome_args', [])
                    target_config['window_settings'] = source_config.get('window_settings', {})
                else:
                    target_config = source_config.copy()

                # 更新实例特定信息
                target_config['instance_name'] = target_name
                target_config['created_date'] = datetime.now().isoformat()
                target_config['last_modified'] = datetime.now().isoformat()

                # 保存目标配置
                with open(target_config_path, 'w', encoding='utf-8') as f:
                    json.dump(target_config, f, indent=2, ensure_ascii=False)

            # 创建Data目录
            data_dir = target_dir / "Data"
            data_dir.mkdir(exist_ok=True)

            # 生成启动脚本
            script_content = self._generate_launch_script_content(target_name, target_config)
            script_path = target_dir / f"chrome_{target_name}.py"

            with open(script_path, 'w', encoding='utf-8') as f:
                f.write(script_content)

            print(f"✅ 实例复制成功: {source_name} -> {target_name}")
            return True

        except Exception as e:
            print(f"❌ 复制实例失败: {e}")
            return False

    def delete_instance(self, instance_name):
        """删除实例
        
        Args:
            instance_name: 实例名称
            
        Returns:
            bool: 删除是否成功
        """
        try:
            instance_dir = self.instances_dir / instance_name
            if not instance_dir.exists():
                print(f"错误：实例 '{instance_name}' 不存在")
                return False
            
            # 删除实例目录
            shutil.rmtree(instance_dir)
            print(f"已删除实例: {instance_name}")
            return True
            
        except Exception as e:
            print(f"删除实例失败: {e}")
            return False

    def is_instance_running(self, instance_name):
        """检查实例是否正在运行

        Args:
            instance_name: 实例名称

        Returns:
            bool: 实例是否正在运行
        """
        try:
            # 检查是否有Chrome进程使用了该实例的用户数据目录
            instance_dir = self.instances_dir / instance_name
            user_data_dir = str(instance_dir / "UserData")

            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                        cmdline = proc.info['cmdline']
                        if cmdline and any(user_data_dir in arg for arg in cmdline):
                            return True
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue

            return False
        except Exception as e:
            print(f"检查实例运行状态失败: {e}")
            return False

    def launch_instance(self, instance_name):
        """启动实例

        Args:
            instance_name: 实例名称

        Returns:
            bool: 启动成功返回True，失败返回False
        """
        try:
            instance_dir = self.instances_dir / instance_name
            if not instance_dir.exists():
                print(f"❌ 实例 {instance_name} 不存在")
                return False

            # 检查是否已经在运行
            if self.is_instance_running(instance_name):
                print(f"⚠️ 实例 {instance_name} 已在运行")
                return True

            # 读取配置
            config_file = instance_dir / "config.json"
            if not config_file.exists():
                print(f"❌ 实例 {instance_name} 配置文件不存在")
                return False

            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)

            # 构建Chrome启动命令
            print(f"🔍 查找Chrome可执行文件...")
            chrome_path = self._find_chrome_executable()
            if not chrome_path:
                print("❌ 未找到Chrome可执行文件")
                print("💡 可尝试的解决方案:")
                print("   1. 安装Google Chrome浏览器")
                print("   2. 下载Chrome便携版到项目目录")
                print("   3. 确保Chrome在PATH环境变量中")
                return False

            print(f"✅ 找到Chrome: {chrome_path}")

            # 验证Chrome文件是否存在
            if not Path(chrome_path).exists():
                print(f"❌ Chrome文件不存在: {chrome_path}")
                return False

            user_data_dir = str(instance_dir / "Data")
            print(f"📁 用户数据目录: {user_data_dir}")

            # 基础参数
            cmd = [
                chrome_path,
                f"--user-data-dir={user_data_dir}",
                "--no-first-run",
                "--no-default-browser-check",
                "--disable-background-timer-throttling"
            ]

            # 添加自定义参数
            chrome_args = config.get("chrome_args", [])
            cmd.extend(chrome_args)

            # 添加主页
            startup_options = config.get("startup_options", {})
            homepage = startup_options.get("homepage", "")
            if homepage:
                cmd.append(homepage)
                print(f"🏠 设置主页: {homepage}")

            print(f"🎯 启动命令: {' '.join(cmd)}")

            # 启动进程
            import subprocess
            print("🚀 启动Chrome进程...")

            try:
                process = subprocess.Popen(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    creationflags=subprocess.CREATE_NEW_PROCESS_GROUP if sys.platform == "win32" else 0
                )

                print(f"✅ Chrome进程已启动，PID: {process.pid}")

                # 等待一下确保启动成功
                time.sleep(3)

                # 检查进程是否还在运行
                poll_result = process.poll()
                if poll_result is not None:
                    print(f"❌ Chrome进程已退出，退出码: {poll_result}")
                    # 读取错误输出
                    try:
                        stdout, stderr = process.communicate(timeout=1)
                        if stderr:
                            print(f"📋 Chrome错误输出: {stderr.decode('utf-8', errors='ignore')}")
                    except:
                        pass
                    return False

                print("✅ Chrome进程正在运行")
                display_name = config.get('display_name', instance_name)
                print(f"✅ 实例 '{display_name}' 启动成功")
                return True

            except Exception as subprocess_error:
                print(f"❌ 启动Chrome进程失败: {subprocess_error}")
                return False

        except Exception as e:
            print(f"❌ 启动实例失败: {e}")
            return False

    def _find_chrome_executable(self):
        """查找Chrome可执行文件"""
        # 常见的Chrome路径
        possible_paths = []

        if sys.platform == "win32":
            username = os.getenv('USERNAME', '')
            possible_paths = [
                # 便携版路径（优先检查）
                "./GoogleChromePortable/App/Chrome-bin/chrome.exe",  # 便携版路径
                "./chrome-win/chrome.exe",  # 便携版路径
                "./chrome/chrome.exe",  # 项目目录下的Chrome
                "./Chrome/chrome.exe",  # 项目目录下的Chrome
                "chrome.exe",  # 当前目录
                # 系统安装路径
                r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
                f"C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\Application\\chrome.exe",
                f"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\Application\\chrome.exe"
            ]
        elif sys.platform == "darwin":
            possible_paths = [
                "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
                "/Applications/Chromium.app/Contents/MacOS/Chromium"
            ]
        else:  # Linux
            possible_paths = [
                "/usr/bin/google-chrome",
                "/usr/bin/google-chrome-stable",
                "/usr/bin/chromium-browser",
                "/usr/bin/chromium"
            ]

        for path in possible_paths:
            if Path(path).exists():
                return path

        # 尝试从PATH中查找
        import shutil
        chrome_names = ["chrome", "google-chrome", "google-chrome-stable", "chromium", "chromium-browser"]
        for name in chrome_names:
            path = shutil.which(name)
            if path:
                return path

        return None

    def stop_instance(self, instance_name):
        """停止实例

        Args:
            instance_name: 实例名称

        Returns:
            bool: 是否成功停止
        """
        try:
            # 查找并终止使用该实例用户数据目录的Chrome进程
            instance_dir = self.instances_dir / instance_name
            user_data_dir = str(instance_dir / "UserData")

            stopped_count = 0
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                        cmdline = proc.info['cmdline']
                        if cmdline and any(user_data_dir in arg for arg in cmdline):
                            proc.terminate()
                            stopped_count += 1
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue

            if stopped_count > 0:
                print(f"已停止 {stopped_count} 个 {instance_name} 实例进程")
                return True
            else:
                print(f"未找到运行中的 {instance_name} 实例")
                return False

        except Exception as e:
            print(f"停止实例失败: {e}")
            return False


def main():
    """命令行接口"""
    import argparse
    
    parser = argparse.ArgumentParser(description="浏览器实例管理器")
    subparsers = parser.add_subparsers(dest="command", help="可用命令")
    
    # 创建实例命令
    create_parser = subparsers.add_parser("create", help="创建新实例")
    create_parser.add_argument("name", help="实例名称")
    create_parser.add_argument("--display-name", help="显示名称")
    create_parser.add_argument("--icon", default="default.ico", help="图标文件名")
    create_parser.add_argument("--description", default="", help="描述")
    create_parser.add_argument("--homepage", default="", help="主页URL")
    
    # 列出实例命令
    list_parser = subparsers.add_parser("list", help="列出所有实例")
    
    # 删除实例命令
    delete_parser = subparsers.add_parser("delete", help="删除实例")
    delete_parser.add_argument("name", help="实例名称")
    
    args = parser.parse_args()
    
    manager = BrowserManager()
    
    if args.command == "create":
        manager.create_instance(
            args.name,
            display_name=args.display_name,
            icon=args.icon,
            description=args.description,
            homepage=args.homepage
        )
    elif args.command == "list":
        instances = manager.list_instances()
        if instances:
            print("现有实例:")
            for instance in instances:
                config = instance["config"]
                print(f"  - {instance['name']} - {config.get('display_name', '')}")
                print(f"    描述: {config.get('description', '无')}")
                print(f"    图标: {config.get('icon', 'default.ico')}")
                print()
        else:
            print("暂无实例")
    elif args.command == "delete":
        manager.delete_instance(args.name)
    else:
        parser.print_help()


if __name__ == "__main__":
    main()
