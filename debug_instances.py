#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试实例加载问题
"""

import sys
import os
import json
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))
sys.path.append(str(Path(__file__).parent / "tools"))

def debug_browser_manager():
    """调试BrowserManager"""
    print("🔍 调试BrowserManager...")
    
    try:
        from browser_manager import BrowserManager
        
        manager = BrowserManager()
        print(f"✅ BrowserManager初始化成功")
        print(f"📁 实例目录: {manager.instances_dir}")
        print(f"📁 目录存在: {manager.instances_dir.exists()}")
        
        if manager.instances_dir.exists():
            print(f"📂 目录内容:")
            for item in manager.instances_dir.iterdir():
                print(f"   - {item.name} ({'目录' if item.is_dir() else '文件'})")
        
        # 测试list_instances
        instances = manager.list_instances()
        print(f"\n📋 实例列表: {len(instances)} 个")
        
        for i, instance in enumerate(instances, 1):
            print(f"\n{i}. 实例: {instance['name']}")
            print(f"   路径: {instance['path']}")
            print(f"   配置: {instance['config']}")
        
        return instances
        
    except Exception as e:
        print(f"❌ BrowserManager调试失败: {e}")
        import traceback
        traceback.print_exc()
        return []

def debug_gui_loading():
    """调试GUI加载"""
    print("\n🔍 调试GUI加载...")
    
    try:
        import tkinter as tk
        from gui_launcher import BrowserLauncherGUI
        
        # 创建临时GUI实例
        root = tk.Tk()
        root.withdraw()
        
        app = BrowserLauncherGUI()
        app.root.withdraw()
        
        print("✅ GUI初始化成功")
        
        # 测试browser_manager
        print(f"📋 GUI中的browser_manager: {app.browser_manager}")
        
        # 测试load_instances
        print("🔄 测试load_instances...")
        instances = app.browser_manager.list_instances()
        print(f"📋 获取到 {len(instances)} 个实例")
        
        for instance in instances:
            print(f"   - {instance['name']}: {instance['config'].get('display_name', '无显示名')}")
        
        app.root.destroy()
        root.destroy()
        
        return instances
        
    except Exception as e:
        print(f"❌ GUI调试失败: {e}")
        import traceback
        traceback.print_exc()
        return []

def debug_instance_cards():
    """调试实例卡片创建"""
    print("\n🔍 调试实例卡片创建...")
    
    try:
        import tkinter as tk
        from gui_launcher import InstanceCard
        
        root = tk.Tk()
        root.withdraw()
        
        # 创建测试框架
        test_frame = tk.Frame(root)
        
        # 测试实例数据
        test_instance = {
            'name': 'test_card',
            'config': {
                'display_name': '测试卡片',
                'description': '测试描述',
                'icon': 'default'
            }
        }
        
        # 创建实例卡片
        card = InstanceCard(
            test_frame,
            test_instance,
            on_launch=lambda x: print(f"启动: {x}"),
            on_menu=lambda x: print(f"菜单: {x}")
        )
        
        print("✅ 实例卡片创建成功")
        
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ 实例卡片调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_config_files():
    """调试配置文件"""
    print("\n🔍 调试配置文件...")
    
    instances_dir = Path(__file__).parent / "instances"
    
    if not instances_dir.exists():
        print("❌ instances目录不存在")
        return False
    
    print(f"📁 instances目录: {instances_dir}")
    
    for item in instances_dir.iterdir():
        if item.is_dir():
            print(f"\n📂 实例目录: {item.name}")
            
            config_path = item / "config.json"
            if config_path.exists():
                try:
                    with open(config_path, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    
                    print(f"   ✅ 配置文件正常")
                    print(f"   📝 显示名: {config.get('display_name', '无')}")
                    print(f"   📝 描述: {config.get('description', '无')}")
                    print(f"   📝 图标: {config.get('icon', '无')}")
                    
                except Exception as e:
                    print(f"   ❌ 配置文件错误: {e}")
            else:
                print(f"   ❌ 缺少config.json")
    
    return True

def main():
    """主调试函数"""
    print("=" * 60)
    print("🔧 实例加载问题调试")
    print("=" * 60)
    
    # 1. 调试配置文件
    debug_config_files()
    
    # 2. 调试BrowserManager
    instances1 = debug_browser_manager()
    
    # 3. 调试GUI加载
    instances2 = debug_gui_loading()
    
    # 4. 调试实例卡片
    debug_instance_cards()
    
    print("\n" + "=" * 60)
    print("📊 调试结果总结")
    print("=" * 60)
    
    print(f"BrowserManager获取实例数: {len(instances1)}")
    print(f"GUI获取实例数: {len(instances2)}")
    
    if len(instances1) > 0 and len(instances2) > 0:
        print("✅ 实例数据获取正常")
        print("🔍 问题可能在GUI显示逻辑")
    elif len(instances1) > 0:
        print("⚠️ BrowserManager正常，GUI加载有问题")
    else:
        print("❌ BrowserManager获取实例失败")
    
    print("\n建议检查:")
    print("1. GUI的load_instances()调用时机")
    print("2. 实例卡片的pack()方法")
    print("3. scrollable_frame的配置")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"\n调试异常: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n按回车键退出...")
    input()
