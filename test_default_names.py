#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试默认名称生成功能
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))
sys.path.append(str(Path(__file__).parent / "tools"))

def test_default_name_generation():
    """测试默认名称生成"""
    print("🧪 测试默认名称生成功能...")
    
    try:
        from gui_dialogs import NewInstanceDialog
        from browser_manager import BrowserManager
        
        # 创建一个模拟的对话框来测试名称生成
        class MockDialog:
            def __init__(self):
                self.browser_manager = BrowserManager()
            
            def generate_default_name(self):
                """生成默认实例名称"""
                try:
                    # 获取现有实例
                    instances = self.browser_manager.list_instances()
                    existing_names = [inst['name'] for inst in instances]
                    
                    # 查找数字后缀的实例名称
                    chinese_numbers = ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十',
                                     '十一', '十二', '十三', '十四', '十五', '十六', '十七', '十八', '十九', '二十']
                    
                    # 检查已存在的"浏览器X"格式的名称
                    used_numbers = set()
                    for name in existing_names:
                        if name.startswith('浏览器'):
                            suffix = name[3:]  # 去掉"浏览器"前缀
                            if suffix in chinese_numbers:
                                used_numbers.add(chinese_numbers.index(suffix) + 1)
                    
                    # 找到第一个未使用的数字
                    for i in range(1, len(chinese_numbers) + 1):
                        if i not in used_numbers:
                            return f"浏览器{chinese_numbers[i-1]}"
                    
                    # 如果前20个都用完了，使用数字
                    for i in range(21, 100):
                        name = f"浏览器{i}"
                        if name not in existing_names:
                            return name
                    
                    # 最后的备选方案
                    return "新浏览器实例"
                    
                except Exception as e:
                    print(f"生成默认名称失败: {e}")
                    return "新浏览器实例"
        
        # 测试名称生成
        mock_dialog = MockDialog()
        
        print("📋 当前实例列表:")
        instances = mock_dialog.browser_manager.list_instances()
        for inst in instances:
            print(f"   - {inst['name']}")
        
        print(f"\n🎯 生成的默认名称: {mock_dialog.generate_default_name()}")
        
        # 测试多次生成（模拟连续创建）
        print("\n🔄 模拟连续创建实例:")
        simulated_names = [inst['name'] for inst in instances]
        
        for i in range(5):
            # 模拟已有这些名称的情况
            mock_dialog.browser_manager.list_instances = lambda: [{'name': name} for name in simulated_names]
            
            new_name = mock_dialog.generate_default_name()
            print(f"   第{i+1}次: {new_name}")
            simulated_names.append(new_name)
        
        return True
        
    except Exception as e:
        print(f"❌ 测试默认名称生成失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_dialog_display():
    """测试对话框显示"""
    print("\n🖼️ 测试对话框显示...")
    
    try:
        import tkinter as tk
        from gui_dialogs import NewInstanceDialog
        
        # 创建测试窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        print("✅ 正在显示新建实例对话框...")
        print("   请检查:")
        print("   1. 实例名称是否自动填入了'浏览器X'格式")
        print("   2. 显示名称是否自动填入了'Chrome - 浏览器X'格式")
        print("   3. 创建和取消按钮是否变大了")
        
        # 创建对话框
        dialog = NewInstanceDialog(root)
        
        # 等待对话框关闭
        root.wait_window(dialog.dialog)
        
        print("✅ 对话框测试完成")
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试对话框显示失败: {e}")
        return False

def test_chinese_number_logic():
    """测试中文数字逻辑"""
    print("\n🔢 测试中文数字逻辑...")
    
    chinese_numbers = ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十',
                      '十一', '十二', '十三', '十四', '十五', '十六', '十七', '十八', '十九', '二十']
    
    # 测试索引转换
    test_cases = [
        ('浏览器一', 1),
        ('浏览器二', 2),
        ('浏览器十', 10),
        ('浏览器十五', 15),
        ('浏览器二十', 20)
    ]
    
    print("📋 测试中文数字索引转换:")
    for name, expected_index in test_cases:
        suffix = name[3:]  # 去掉"浏览器"前缀
        if suffix in chinese_numbers:
            actual_index = chinese_numbers.index(suffix) + 1
            status = "✅" if actual_index == expected_index else "❌"
            print(f"   {status} {name} -> 索引 {actual_index} (期望 {expected_index})")
        else:
            print(f"   ❌ {name} -> 未找到对应的中文数字")
    
    # 测试索引到中文数字的转换
    print("\n📋 测试索引到中文数字转换:")
    for i in range(1, 21):
        chinese_num = chinese_numbers[i-1]
        name = f"浏览器{chinese_num}"
        print(f"   索引 {i} -> {name}")
    
    return True

def main():
    """主函数"""
    print("=" * 60)
    print("🧪 默认名称生成功能测试")
    print("=" * 60)
    
    tests = [
        ("中文数字逻辑", test_chinese_number_logic),
        ("默认名称生成", test_default_name_generation),
        ("对话框显示", test_dialog_display)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*15} {test_name} {'='*15}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 60)
    print("📊 测试结果")
    print("=" * 60)
    
    if passed == total:
        print("🎉 所有测试通过！")
        print("\n✅ 新功能:")
        print("• 自动生成'浏览器一'、'浏览器二'等默认名称")
        print("• 自动生成对应的显示名称'Chrome - 浏览器X'")
        print("• 按钮大小调整为更大更易点击")
        print("• 智能避免重复名称")
        
        print("\n🚀 使用方法:")
        print("1. 点击'新建实例'按钮")
        print("2. 对话框会自动填入默认名称")
        print("3. 可以直接点击'创建'或修改名称后创建")
        print("4. 连续创建会自动递增数字")
        
    else:
        print(f"⚠️ {total - passed}/{total} 个测试失败")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"\n测试异常: {e}")
    
    print("\n按回车键退出...")
    input()
