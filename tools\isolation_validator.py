#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据隔离验证工具
验证每个浏览器实例的数据是否完全隔离
"""

import os
import json
import hashlib
from pathlib import Path
from datetime import datetime


class IsolationValidator:
    """数据隔离验证器"""
    
    def __init__(self, base_dir=None):
        """初始化验证器"""
        if base_dir is None:
            self.base_dir = Path(__file__).parent.parent
        else:
            self.base_dir = Path(base_dir)
            
        self.instances_dir = self.base_dir / "instances"
    
    def validate_all_instances(self):
        """验证所有实例的数据隔离"""
        print("开始验证数据隔离...")
        print("=" * 50)

        if not self.instances_dir.exists():
            print("实例目录不存在")
            return False

        instances = []
        for item in self.instances_dir.iterdir():
            if item.is_dir() and (item / "config.json").exists():
                instances.append(item)

        if len(instances) < 2:
            print("需要至少2个实例才能验证数据隔离")
            return True

        print(f"找到 {len(instances)} 个实例:")
        for instance in instances:
            print(f"  - {instance.name}")
        print()
        
        # 验证数据目录隔离
        isolation_results = []
        for i, instance in enumerate(instances):
            result = self._validate_instance_isolation(instance, instances)
            isolation_results.append(result)
        
        # 生成验证报告
        self._generate_report(isolation_results)
        
        # 检查是否所有实例都通过验证
        all_passed = all(result["passed"] for result in isolation_results)
        
        if all_passed:
            print("所有实例数据隔离验证通过！")
        else:
            print("发现数据隔离问题，请检查报告")
        
        return all_passed
    
    def _validate_instance_isolation(self, instance_path, all_instances):
        """验证单个实例的数据隔离"""
        instance_name = instance_path.name
        data_dir = instance_path / "Data"
        
        result = {
            "instance": instance_name,
            "passed": True,
            "issues": [],
            "data_dir": str(data_dir),
            "data_exists": data_dir.exists(),
            "profile_exists": (data_dir / "profile").exists(),
            "unique_data": True,
            "config_valid": True
        }
        
        print(f"验证实例: {instance_name}")

        # 检查数据目录是否存在
        if not data_dir.exists():
            result["passed"] = False
            result["issues"].append("数据目录不存在")
            print(f"  数据目录不存在: {data_dir}")
            return result

        # 检查profile目录是否存在
        profile_dir = data_dir / "profile"
        if not profile_dir.exists():
            result["passed"] = False
            result["issues"].append("profile目录不存在")
            print(f"  profile目录不存在: {profile_dir}")
            return result

        # 检查配置文件
        config_path = instance_path / "config.json"
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            print(f"  配置文件有效")
        except Exception as e:
            result["passed"] = False
            result["config_valid"] = False
            result["issues"].append(f"配置文件无效: {e}")
            print(f"  配置文件无效: {e}")
        
        # 检查数据目录是否独立
        data_signature = self._get_directory_signature(data_dir)
        for other_instance in all_instances:
            if other_instance == instance_path:
                continue
            
            other_data_dir = other_instance / "Data"
            if other_data_dir.exists():
                other_signature = self._get_directory_signature(other_data_dir)
                if data_signature == other_signature:
                    result["passed"] = False
                    result["unique_data"] = False
                    result["issues"].append(f"与实例 {other_instance.name} 的数据目录相同")
                    print(f"  与实例 {other_instance.name} 的数据目录相同")

        # 检查关键文件是否存在
        key_files = [
            "profile/Local State",
            "profile/Default",
        ]

        for key_file in key_files:
            file_path = data_dir / key_file
            if file_path.exists():
                print(f"  关键文件存在: {key_file}")
            else:
                print(f"  关键文件缺失: {key_file}")

        if result["passed"]:
            print(f"  实例 {instance_name} 数据隔离验证通过")
        else:
            print(f"  实例 {instance_name} 数据隔离验证失败")
        
        print()
        return result
    
    def _get_directory_signature(self, directory):
        """获取目录的签名（用于比较是否相同）"""
        try:
            # 获取目录的基本信息
            stat = directory.stat()
            # 使用创建时间和修改时间作为签名的一部分
            signature_data = f"{stat.st_ctime}_{stat.st_mtime}_{stat.st_size}"
            
            # 检查一些关键文件的存在性
            key_files = ["profile/Local State", "profile/Default"]
            for key_file in key_files:
                file_path = directory / key_file
                if file_path.exists():
                    file_stat = file_path.stat()
                    signature_data += f"_{key_file}_{file_stat.st_mtime}"
            
            return hashlib.md5(signature_data.encode()).hexdigest()
        except Exception:
            return str(directory)
    
    def _generate_report(self, results):
        """生成验证报告"""
        print("数据隔离验证报告")
        print("=" * 50)

        total_instances = len(results)
        passed_instances = sum(1 for r in results if r["passed"])

        print(f"总实例数: {total_instances}")
        print(f"通过验证: {passed_instances}")
        print(f"验证失败: {total_instances - passed_instances}")
        print()

        for result in results:
            status = "[通过]" if result["passed"] else "[失败]"
            print(f"{result['instance']}: {status}")
            
            if not result["passed"]:
                for issue in result["issues"]:
                    print(f"  - {issue}")
            print()
    
    def create_test_instances(self, count=3):
        """创建测试实例用于验证"""
        print(f"创建 {count} 个测试实例...")
        
        # 导入浏览器管理器
        import sys
        sys.path.insert(0, str(self.base_dir / "tools"))
        from browser_manager import BrowserManager
        
        manager = BrowserManager()
        
        test_configs = [
            {"name": "work", "display": "工作浏览器", "homepage": "https://www.google.com"},
            {"name": "personal", "display": "个人浏览器", "homepage": "https://www.baidu.com"},
            {"name": "shopping", "display": "购物浏览器", "homepage": "https://www.taobao.com"},
            {"name": "social", "display": "社交浏览器", "homepage": "https://www.weibo.com"},
            {"name": "dev", "display": "开发浏览器", "homepage": "https://github.com"},
        ]
        
        created_count = 0
        for i in range(min(count, len(test_configs))):
            config = test_configs[i]
            success = manager.create_instance(
                instance_name=config["name"],
                display_name=config["display"],
                description=f"测试实例 - {config['display']}",
                homepage=config["homepage"]
            )
            if success:
                created_count += 1
        
        print(f"成功创建 {created_count} 个测试实例")
        return created_count


def main():
    """命令行接口"""
    import argparse
    
    parser = argparse.ArgumentParser(description="数据隔离验证工具")
    subparsers = parser.add_subparsers(dest="command", help="可用命令")
    
    # 验证命令
    validate_parser = subparsers.add_parser("validate", help="验证数据隔离")
    
    # 创建测试实例命令
    create_parser = subparsers.add_parser("create-test", help="创建测试实例")
    create_parser.add_argument("--count", type=int, default=3, help="创建实例数量")
    
    args = parser.parse_args()
    
    validator = IsolationValidator()
    
    if args.command == "validate":
        validator.validate_all_instances()
    elif args.command == "create-test":
        validator.create_test_instances(args.count)
    else:
        parser.print_help()


if __name__ == "__main__":
    main()
