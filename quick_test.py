#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速功能测试脚本
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def test_imports():
    """测试模块导入"""
    print("🔍 测试模块导入...")
    
    try:
        # 测试GUI模块
        from gui_launcher import BrowserLauncherGUI, ProcessMonitor, InstanceCard
        print("✅ gui_launcher 导入成功")
        
        from gui_dialogs import NewInstanceDialog, BatchConfigDialog
        print("✅ gui_dialogs 导入成功")
        
        # 测试工具模块
        sys.path.append(str(Path(__file__).parent / "tools"))
        from browser_manager import BrowserManager
        from icon_manager import IconManager
        print("✅ 工具模块导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        return False

def test_chrome_detection():
    """测试Chrome检测"""
    print("🔍 测试Chrome检测...")
    
    try:
        from gui_launcher import BrowserLauncherGUI
        
        # 创建临时实例测试Chrome检测
        class TempApp:
            def find_chrome_executable(self):
                possible_paths = [
                    Path(__file__).parent / "GoogleChromePortable" / "App" / "Chrome-bin" / "chrome.exe",
                    Path(__file__).parent / "GoogleChromePortable" / "GoogleChromePortable.exe",
                    Path("C:/Program Files/Google/Chrome/Application/chrome.exe"),
                    Path("C:/Program Files (x86)/Google/Chrome/Application/chrome.exe"),
                    Path.home() / "AppData/Local/Google/Chrome/Application/chrome.exe"
                ]
                
                for path in possible_paths:
                    if path.exists():
                        return path
                return None
        
        app = TempApp()
        chrome_path = app.find_chrome_executable()
        
        if chrome_path:
            print(f"✅ Chrome检测成功: {chrome_path}")
        else:
            print("⚠️ 未检测到Chrome，但检测功能正常")
        
        return True
        
    except Exception as e:
        print(f"❌ Chrome检测失败: {e}")
        return False

def test_url_validation():
    """测试URL验证"""
    print("🔍 测试URL验证...")
    
    try:
        import re
        
        def is_valid_url(url):
            url_pattern = re.compile(
                r'^https?://'
                r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'
                r'localhost|'
                r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'
                r'(?::\d+)?'
                r'(?:/?|[/?]\S+)$', re.IGNORECASE)
            return url_pattern.match(url) is not None
        
        test_urls = [
            ("https://www.google.com", True),
            ("http://localhost:8080", True),
            ("invalid-url", False),
            ("ftp://example.com", False)
        ]
        
        all_passed = True
        for url, expected in test_urls:
            result = is_valid_url(url)
            if result == expected:
                print(f"✅ URL验证: {url} -> {result}")
            else:
                print(f"❌ URL验证错误: {url} -> {result} (期望: {expected})")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ URL验证测试失败: {e}")
        return False

def test_instance_operations():
    """测试实例操作"""
    print("🔍 测试实例操作...")
    
    try:
        sys.path.append(str(Path(__file__).parent / "tools"))
        from browser_manager import BrowserManager
        
        manager = BrowserManager()
        
        # 测试列出实例
        instances = manager.list_instances()
        print(f"✅ 实例列表: {len(instances)} 个实例")
        
        # 测试创建和删除临时实例
        test_name = "quick_test_temp"
        
        # 清理可能存在的测试实例
        try:
            manager.delete_instance(test_name)
        except:
            pass
        
        # 创建实例
        success = manager.create_instance(
            test_name,
            display_name="快速测试实例",
            description="临时测试实例"
        )
        
        if success:
            print("✅ 实例创建成功")
            
            # 删除测试实例
            delete_success = manager.delete_instance(test_name)
            if delete_success:
                print("✅ 实例删除成功")
                return True
            else:
                print("❌ 实例删除失败")
                return False
        else:
            print("❌ 实例创建失败")
            return False
            
    except Exception as e:
        print(f"❌ 实例操作测试失败: {e}")
        return False

def test_file_integrity():
    """测试文件完整性"""
    print("🔍 测试文件完整性...")
    
    critical_files = [
        'gui_launcher.py',
        'gui_dialogs.py',
        '启动GUI界面.py',
        '启动GUI界面.bat'
    ]
    
    all_exist = True
    for filename in critical_files:
        filepath = Path(filename)
        if filepath.exists():
            size = filepath.stat().st_size
            print(f"✅ {filename} (大小: {size} bytes)")
        else:
            print(f"❌ 缺少文件: {filename}")
            all_exist = False
    
    return all_exist

def main():
    """主测试函数"""
    print("=" * 50)
    print("🔧 快速功能测试")
    print("=" * 50)
    
    tests = [
        ("文件完整性", test_file_integrity),
        ("模块导入", test_imports),
        ("Chrome检测", test_chrome_detection),
        ("URL验证", test_url_validation),
        ("实例操作", test_instance_operations)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*15} {test_name} {'='*15}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 50)
    print("📊 测试结果")
    print("=" * 50)
    
    if passed == total:
        print("🎉 所有测试通过！")
        print("\n✅ 功能状态:")
        print("• 文件完整性 ✓")
        print("• 模块导入 ✓")
        print("• Chrome检测 ✓")
        print("• 输入验证 ✓")
        print("• 实例管理 ✓")
        
        print("\n🚀 GUI界面可以正常使用！")
        print("\n启动方式：")
        print("1. 双击 '启动GUI界面.bat'")
        print("2. 运行 'python gui_launcher.py'")
        
        return True
    else:
        print(f"⚠️ {total - passed}/{total} 个测试失败")
        return False

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"\n测试异常: {e}")
    
    print("\n按回车键退出...")
    input()
