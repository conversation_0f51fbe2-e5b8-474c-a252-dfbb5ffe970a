#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设置Web应用环境
"""

import os
import shutil
from pathlib import Path

def setup_static_files():
    """设置静态文件"""
    print("🔧 设置静态文件...")
    
    # 创建静态文件目录
    static_dir = Path("static")
    static_dir.mkdir(exist_ok=True)
    
    # 创建子目录
    (static_dir / "css").mkdir(exist_ok=True)
    (static_dir / "js").mkdir(exist_ok=True)
    (static_dir / "images").mkdir(exist_ok=True)
    
    # 复制图标文件到static/images
    icons_dir = Path("icons")
    static_images_dir = static_dir / "images"
    
    if icons_dir.exists():
        print("📁 复制图标文件...")
        for icon_file in icons_dir.glob("*.ico"):
            dest_file = static_images_dir / icon_file.name
            if not dest_file.exists():
                shutil.copy2(icon_file, dest_file)
                print(f"   ✅ {icon_file.name}")
    
    print("✅ 静态文件设置完成")

def check_dependencies():
    """检查依赖"""
    print("🔍 检查依赖...")
    
    try:
        import flask
        print("✅ Flask 已安装")
    except ImportError:
        print("❌ Flask 未安装，正在安装...")
        os.system("pip install flask")
    
    try:
        from tools.browser_manager import BrowserManager
        print("✅ BrowserManager 可用")
    except ImportError as e:
        print(f"❌ BrowserManager 导入失败: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("=" * 50)
    print("🚀 设置现代化Web界面")
    print("=" * 50)
    
    # 设置静态文件
    setup_static_files()
    
    # 检查依赖
    if not check_dependencies():
        print("❌ 依赖检查失败")
        return
    
    print("\n✅ 环境设置完成！")
    print("\n🌐 启动Web应用:")
    print("   python web_app.py")
    print("\n📍 访问地址:")
    print("   http://127.0.0.1:5000")

if __name__ == "__main__":
    main()
