#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Chrome路径测试脚本
"""

import os
import sys
from pathlib import Path

def find_chrome_executable():
    """查找Chrome可执行文件"""
    print("🔍 开始查找Chrome可执行文件...")
    
    # 常见的Chrome路径
    possible_paths = []

    if sys.platform == "win32":
        username = os.getenv('USERNAME', '')
        print(f"📍 当前用户: {username}")
        
        possible_paths = [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
            f"C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\Application\\chrome.exe",
            f"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\Application\\chrome.exe",
            "./chrome-win/chrome.exe",  # 便携版路径
            "./GoogleChromePortable/App/Chrome-bin/chrome.exe",  # 便携版路径
            "./chrome/chrome.exe",  # 项目目录下的Chrome
            "./Chrome/chrome.exe",  # 项目目录下的Chrome
            "chrome.exe"  # 当前目录
        ]
    elif sys.platform == "darwin":
        possible_paths = [
            "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
            "/Applications/Chromium.app/Contents/MacOS/Chromium"
        ]
    else:  # Linux
        possible_paths = [
            "/usr/bin/google-chrome",
            "/usr/bin/google-chrome-stable",
            "/usr/bin/chromium-browser",
            "/usr/bin/chromium"
        ]

    print(f"📋 检查 {len(possible_paths)} 个可能的路径:")
    for i, path in enumerate(possible_paths, 1):
        exists = Path(path).exists()
        status = "✅" if exists else "❌"
        print(f"  {i:2d}. {status} {path}")
        if exists:
            print(f"      🎯 找到Chrome: {path}")
            return path

    # 尝试从PATH中查找
    print("\n🔍 尝试从PATH环境变量中查找...")
    import shutil
    chrome_names = ["chrome", "google-chrome", "google-chrome-stable", "chromium", "chromium-browser"]
    for name in chrome_names:
        path = shutil.which(name)
        if path:
            print(f"✅ 在PATH中找到: {name} -> {path}")
            return path
        else:
            print(f"❌ 在PATH中未找到: {name}")

    print("\n❌ 未找到Chrome可执行文件")
    return None

def test_chrome_launch(chrome_path):
    """测试Chrome启动"""
    if not chrome_path:
        print("❌ 无法测试启动：Chrome路径为空")
        return False
    
    print(f"\n🚀 测试Chrome启动: {chrome_path}")
    
    try:
        import subprocess
        # 测试Chrome版本
        result = subprocess.run([chrome_path, "--version"], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✅ Chrome版本: {result.stdout.strip()}")
            return True
        else:
            print(f"❌ Chrome启动失败: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print("⏰ Chrome启动超时")
        return False
    except Exception as e:
        print(f"❌ Chrome启动异常: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🧪 Chrome路径检测测试")
    print("=" * 60)
    
    chrome_path = find_chrome_executable()
    
    if chrome_path:
        print(f"\n🎯 最终结果: {chrome_path}")
        test_chrome_launch(chrome_path)
    else:
        print("\n❌ 测试失败: 未找到Chrome")
        print("\n💡 解决方案:")
        print("   1. 安装Google Chrome浏览器")
        print("   2. 下载Chrome便携版到项目目录")
        print("   3. 确保Chrome在PATH环境变量中")
    
    print("\n" + "=" * 60)
