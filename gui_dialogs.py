#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI对话框模块
包含新建实例、编辑实例、设置等对话框
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, colorchooser
import os
import sys
import json
from pathlib import Path

# 添加tools目录到Python路径
sys.path.append(str(Path(__file__).parent / "tools"))

try:
    from browser_manager import BrowserManager
    from icon_manager import IconManager
    from batch_configurator import BatchConfigurator
except ImportError as e:
    print(f"导入工具模块失败: {e}")


class NewInstanceDialog:
    """新建实例对话框"""
    
    def __init__(self, parent, on_success=None):
        self.parent = parent
        self.on_success = on_success
        self.result = None

        self.browser_manager = BrowserManager()
        self.icon_manager = IconManager()

        self.create_dialog()
        
    def create_dialog(self):
        """创建对话框"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("新建浏览器实例")
        self.dialog.geometry("500x400")
        self.dialog.resizable(False, False)
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.center_dialog()
        
        # 创建界面
        self.create_widgets()

        # 设置默认名称
        self.set_default_name()

    def generate_default_name(self):
        """生成默认实例名称"""
        try:
            # 获取现有实例
            instances = self.browser_manager.list_instances()
            existing_names = [inst['name'] for inst in instances]

            # 查找数字后缀的实例名称
            chinese_numbers = ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十',
                             '十一', '十二', '十三', '十四', '十五', '十六', '十七', '十八', '十九', '二十']

            # 检查已存在的"浏览器X"格式的名称
            used_numbers = set()
            for name in existing_names:
                if name.startswith('浏览器'):
                    suffix = name[3:]  # 去掉"浏览器"前缀
                    if suffix in chinese_numbers:
                        used_numbers.add(chinese_numbers.index(suffix) + 1)

            # 找到第一个未使用的数字
            for i in range(1, len(chinese_numbers) + 1):
                if i not in used_numbers:
                    return f"浏览器{chinese_numbers[i-1]}"

            # 如果前20个都用完了，使用数字
            for i in range(21, 100):
                name = f"浏览器{i}"
                if name not in existing_names:
                    return name

            # 最后的备选方案
            return "新浏览器实例"

        except Exception as e:
            print(f"生成默认名称失败: {e}")
            return "新浏览器实例"

    def set_default_name(self):
        """设置默认名称和显示名称"""
        default_name = self.generate_default_name()
        self.name_var.set(default_name)

        # 生成对应的显示名称
        if default_name.startswith('浏览器'):
            display_name = f"Chrome - {default_name}"
            self.display_var.set(display_name)

    def center_dialog(self):
        """居中显示对话框"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (400 // 2)
        self.dialog.geometry(f"500x400+{x}+{y}")
        
    def create_widgets(self):
        """创建控件"""
        main_frame = tk.Frame(self.dialog, padx=20, pady=20)
        main_frame.pack(fill='both', expand=True)
        
        # 标题
        title_label = tk.Label(
            main_frame,
            text="创建新的浏览器实例",
            font=('Arial', 14, 'bold')
        )
        title_label.pack(pady=(0, 20))
        
        # 实例名称
        name_frame = tk.Frame(main_frame)
        name_frame.pack(fill='x', pady=5)
        
        tk.Label(name_frame, text="实例名称*:", width=12, anchor='w').pack(side='left')
        self.name_var = tk.StringVar()
        name_entry = tk.Entry(name_frame, textvariable=self.name_var, font=('Arial', 10))
        name_entry.pack(side='left', fill='x', expand=True, padx=(10, 0))
        name_entry.focus()
        
        # 显示名称
        display_frame = tk.Frame(main_frame)
        display_frame.pack(fill='x', pady=5)
        
        tk.Label(display_frame, text="显示名称:", width=12, anchor='w').pack(side='left')
        self.display_var = tk.StringVar()
        display_entry = tk.Entry(display_frame, textvariable=self.display_var, font=('Arial', 10))
        display_entry.pack(side='left', fill='x', expand=True, padx=(10, 0))
        
        # 描述
        desc_frame = tk.Frame(main_frame)
        desc_frame.pack(fill='x', pady=5)
        
        tk.Label(desc_frame, text="描述:", width=12, anchor='w').pack(side='left')
        self.desc_var = tk.StringVar()
        desc_entry = tk.Entry(desc_frame, textvariable=self.desc_var, font=('Arial', 10))
        desc_entry.pack(side='left', fill='x', expand=True, padx=(10, 0))
        
        # 主页URL
        homepage_frame = tk.Frame(main_frame)
        homepage_frame.pack(fill='x', pady=5)
        
        tk.Label(homepage_frame, text="主页URL:", width=12, anchor='w').pack(side='left')
        self.homepage_var = tk.StringVar()
        homepage_entry = tk.Entry(homepage_frame, textvariable=self.homepage_var, font=('Arial', 10))
        homepage_entry.pack(side='left', fill='x', expand=True, padx=(10, 0))
        
        # 图标选择
        icon_frame = tk.Frame(main_frame)
        icon_frame.pack(fill='x', pady=5)
        
        tk.Label(icon_frame, text="图标:", width=12, anchor='w').pack(side='left')
        
        icon_select_frame = tk.Frame(icon_frame)
        icon_select_frame.pack(side='left', fill='x', expand=True, padx=(10, 0))
        
        self.icon_var = tk.StringVar(value="default")
        icon_combo = ttk.Combobox(
            icon_select_frame,
            textvariable=self.icon_var,
            values=self.get_available_icons(),
            state='readonly',
            width=15
        )
        icon_combo.pack(side='left')
        
        icon_btn = tk.Button(
            icon_select_frame,
            text="浏览...",
            command=self.browse_icon,
            width=8
        )
        icon_btn.pack(side='left', padx=(10, 0))
        
        # Chrome参数
        args_frame = tk.Frame(main_frame)
        args_frame.pack(fill='x', pady=10)
        
        tk.Label(args_frame, text="Chrome参数:", anchor='w').pack(fill='x')
        
        args_text_frame = tk.Frame(args_frame)
        args_text_frame.pack(fill='x', pady=(5, 0))
        
        self.args_text = tk.Text(args_text_frame, height=4, font=('Consolas', 9))
        args_scrollbar = ttk.Scrollbar(args_text_frame, orient="vertical", command=self.args_text.yview)
        self.args_text.configure(yscrollcommand=args_scrollbar.set)
        
        self.args_text.pack(side="left", fill="both", expand=True)
        args_scrollbar.pack(side="right", fill="y")
        
        # 默认参数
        default_args = [
            "--no-first-run",
            "--no-default-browser-check",
            "--disable-background-timer-throttling"
        ]
        self.args_text.insert('1.0', '\n'.join(default_args))
        
        # 按钮区域
        button_frame = tk.Frame(main_frame)
        button_frame.pack(fill='x', pady=(20, 0))
        
        cancel_btn = tk.Button(
            button_frame,
            text="取消",
            command=self.cancel,
            width=15,
            height=2,
            font=('Arial', 10)
        )
        cancel_btn.pack(side='right', padx=(10, 0))

        create_btn = tk.Button(
            button_frame,
            text="创建",
            command=self.create_instance,
            bg='#4CAF50',
            fg='white',
            width=15,
            height=2,
            font=('Arial', 10, 'bold')
        )
        create_btn.pack(side='right')
        
        # 绑定回车键
        self.dialog.bind('<Return>', lambda e: self.create_instance())
        self.dialog.bind('<Escape>', lambda e: self.cancel())
        
    def get_available_icons(self):
        """获取可用图标列表"""
        try:
            icons = self.icon_manager.list_available_icons()
            icon_names = [icon['name'] for icon in icons]
            return icon_names if icon_names else ['default']
        except:
            return ['default', 'work', 'personal', 'shopping', 'development']
    
    def browse_icon(self):
        """浏览选择图标文件"""
        file_path = filedialog.askopenfilename(
            title="选择图标文件",
            filetypes=[
                ("图标文件", "*.ico"),
                ("图片文件", "*.png *.jpg *.jpeg *.gif *.bmp"),
                ("所有文件", "*.*")
            ]
        )
        
        if file_path:
            # 这里可以添加图标导入功能
            messagebox.showinfo("提示", "图标导入功能开发中...")
    
    def create_instance(self):
        """创建实例"""
        # 验证输入
        name = self.name_var.get().strip()
        if not name:
            messagebox.showerror("错误", "请输入实例名称")
            return
        
        # 检查名称是否合法
        if not name.replace('_', '').replace('-', '').isalnum():
            messagebox.showerror("错误", "实例名称只能包含字母、数字、下划线和连字符")
            return

        # 检查名称长度
        if len(name) < 2 or len(name) > 50:
            messagebox.showerror("错误", "实例名称长度应在2-50个字符之间")
            return

        # 检查是否已存在
        try:
            existing_instances = self.browser_manager.list_instances()
            if any(inst['name'] == name for inst in existing_instances):
                messagebox.showerror("错误", f"实例名称 '{name}' 已存在，请使用其他名称")
                return
        except Exception as e:
            print(f"检查实例列表失败: {e}")
        
        try:
            # 获取参数
            display_name = self.display_var.get().strip() or name
            description = self.desc_var.get().strip()
            homepage = self.homepage_var.get().strip()
            icon = self.icon_var.get()

            # 验证URL格式
            if homepage and not self.is_valid_url(homepage):
                messagebox.showerror("错误", "请输入有效的URL地址（如：https://www.google.com）")
                return
            
            # 获取Chrome参数
            args_text = self.args_text.get('1.0', 'end-1c').strip()
            chrome_args = [arg.strip() for arg in args_text.split('\n') if arg.strip()]
            
            # 创建实例
            success = self.browser_manager.create_instance(
                name,
                display_name=display_name,
                description=description,
                homepage=homepage,
                icon=icon,
                chrome_args=chrome_args
            )
            
            if success:
                messagebox.showinfo("成功", f"实例 '{name}' 创建成功！")
                self.result = {
                    'name': name,
                    'display_name': display_name,
                    'description': description,
                    'homepage': homepage,
                    'icon': icon
                }
                
                if self.on_success:
                    self.on_success(self.result)
                
                self.dialog.destroy()
            else:
                messagebox.showerror("错误", "创建实例失败")
                
        except Exception as e:
            messagebox.showerror("错误", f"创建实例失败: {e}")
    
    def is_valid_url(self, url):
        """验证URL格式"""
        import re
        url_pattern = re.compile(
            r'^https?://'  # http:// or https://
            r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
            r'localhost|'  # localhost...
            r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
            r'(?::\d+)?'  # optional port
            r'(?:/?|[/?]\S+)$', re.IGNORECASE)
        return url_pattern.match(url) is not None

    def cancel(self):
        """取消创建"""
        self.dialog.destroy()


class BatchConfigDialog:
    """批量配置对话框"""
    
    def __init__(self, parent, on_success=None):
        self.parent = parent
        self.on_success = on_success
        
        self.batch_configurator = BatchConfigurator()
        
        self.create_dialog()
        
    def create_dialog(self):
        """创建对话框"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("批量配置实例")
        self.dialog.geometry("600x500")
        self.dialog.resizable(True, True)
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.center_dialog()
        
        # 创建界面
        self.create_widgets()
        
    def center_dialog(self):
        """居中显示对话框"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (600 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (500 // 2)
        self.dialog.geometry(f"600x500+{x}+{y}")
        
    def create_widgets(self):
        """创建控件"""
        main_frame = tk.Frame(self.dialog, padx=20, pady=20)
        main_frame.pack(fill='both', expand=True)
        
        # 标题
        title_label = tk.Label(
            main_frame,
            text="批量配置浏览器实例",
            font=('Arial', 14, 'bold')
        )
        title_label.pack(pady=(0, 20))
        
        # 配置模板选择
        template_frame = tk.Frame(main_frame)
        template_frame.pack(fill='x', pady=10)
        
        tk.Label(template_frame, text="选择配置模板:", font=('Arial', 10, 'bold')).pack(anchor='w')
        
        # 模板列表
        template_list_frame = tk.Frame(template_frame)
        template_list_frame.pack(fill='both', expand=True, pady=(10, 0))
        
        self.template_listbox = tk.Listbox(template_list_frame, height=8)
        template_scrollbar = ttk.Scrollbar(template_list_frame, orient="vertical", command=self.template_listbox.yview)
        self.template_listbox.configure(yscrollcommand=template_scrollbar.set)
        
        self.template_listbox.pack(side="left", fill="both", expand=True)
        template_scrollbar.pack(side="right", fill="y")
        
        # 加载模板
        self.load_templates()
        
        # 模板预览
        preview_frame = tk.Frame(main_frame)
        preview_frame.pack(fill='both', expand=True, pady=10)
        
        tk.Label(preview_frame, text="模板预览:", font=('Arial', 10, 'bold')).pack(anchor='w')
        
        preview_text_frame = tk.Frame(preview_frame)
        preview_text_frame.pack(fill='both', expand=True, pady=(10, 0))
        
        self.preview_text = tk.Text(preview_text_frame, height=10, font=('Consolas', 9), state='disabled')
        preview_scrollbar = ttk.Scrollbar(preview_text_frame, orient="vertical", command=self.preview_text.yview)
        self.preview_text.configure(yscrollcommand=preview_scrollbar.set)
        
        self.preview_text.pack(side="left", fill="both", expand=True)
        preview_scrollbar.pack(side="right", fill="y")
        
        # 绑定选择事件
        self.template_listbox.bind('<<ListboxSelect>>', self.on_template_select)
        
        # 按钮区域
        button_frame = tk.Frame(main_frame)
        button_frame.pack(fill='x', pady=(20, 0))
        
        cancel_btn = tk.Button(
            button_frame,
            text="取消",
            command=self.cancel,
            width=15,
            height=2,
            font=('Arial', 10)
        )
        cancel_btn.pack(side='right', padx=(10, 0))

        apply_btn = tk.Button(
            button_frame,
            text="应用配置",
            command=self.apply_config,
            bg='#4CAF50',
            fg='white',
            width=15,
            height=2,
            font=('Arial', 10, 'bold')
        )
        apply_btn.pack(side='right')
        
    def load_templates(self):
        """加载配置模板"""
        try:
            templates = self.batch_configurator.list_templates()
            
            self.template_listbox.delete(0, tk.END)
            self.templates = {}
            
            for template in templates:
                display_name = f"{template['name']} - {template.get('description', '无描述')}"
                self.template_listbox.insert(tk.END, display_name)
                self.templates[display_name] = template
                
        except Exception as e:
            messagebox.showerror("错误", f"加载模板失败: {e}")
    
    def on_template_select(self, event):
        """模板选择事件"""
        selection = self.template_listbox.curselection()
        if selection:
            template_name = self.template_listbox.get(selection[0])
            template = self.templates.get(template_name)
            
            if template:
                self.show_template_preview(template)
    
    def show_template_preview(self, template):
        """显示模板预览"""
        self.preview_text.config(state='normal')
        self.preview_text.delete('1.0', tk.END)
        
        preview_content = f"""模板名称: {template['name']}
描述: {template.get('description', '无描述')}
实例数量: {len(template.get('instances', []))}

实例列表:
"""
        
        for i, instance in enumerate(template.get('instances', []), 1):
            preview_content += f"""
{i}. {instance.get('name', '未命名')}
   显示名称: {instance.get('display_name', '未设置')}
   描述: {instance.get('description', '无描述')}
   主页: {instance.get('homepage', '未设置')}
   图标: {instance.get('icon', 'default')}"""
        
        self.preview_text.insert('1.0', preview_content)
        self.preview_text.config(state='disabled')
    
    def apply_config(self):
        """应用配置"""
        selection = self.template_listbox.curselection()
        if not selection:
            messagebox.showerror("错误", "请选择一个配置模板")
            return
        
        template_name = self.template_listbox.get(selection[0])
        template = self.templates.get(template_name)
        
        if not template:
            messagebox.showerror("错误", "无效的模板")
            return
        
        # 确认对话框
        instance_count = len(template.get('instances', []))
        result = messagebox.askyesno(
            "确认应用",
            f"将创建 {instance_count} 个浏览器实例。\n确定要继续吗？"
        )
        
        if result:
            try:
                # 应用配置
                success_count = self.batch_configurator.create_instances_from_template(template)
                
                messagebox.showinfo(
                    "完成",
                    f"批量配置完成！\n成功创建 {success_count} 个实例。"
                )
                
                if self.on_success:
                    self.on_success()
                
                self.dialog.destroy()
                
            except Exception as e:
                messagebox.showerror("错误", f"批量配置失败: {e}")
    
    def cancel(self):
        """取消"""
        self.dialog.destroy()


class EditInstanceDialog:
    """编辑实例对话框"""

    def __init__(self, parent, instance_name, on_success=None):
        self.parent = parent
        self.instance_name = instance_name
        self.on_success = on_success
        self.result = None

        self.browser_manager = BrowserManager()
        self.icon_manager = IconManager()

        # 加载现有配置
        self.load_instance_config()

        if self.current_config:
            self.create_dialog()
        else:
            messagebox.showerror("错误", f"无法加载实例配置: {instance_name}")

    def load_instance_config(self):
        """加载实例配置"""
        try:
            instances = self.browser_manager.list_instances()
            self.current_config = None

            for instance in instances:
                if instance['name'] == self.instance_name:
                    self.current_config = instance['config'].copy()
                    break

            if not self.current_config:
                print(f"❌ 未找到实例配置: {self.instance_name}")

        except Exception as e:
            print(f"❌ 加载实例配置失败: {e}")
            self.current_config = None

    def create_dialog(self):
        """创建对话框"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title(f"编辑实例 - {self.instance_name}")
        self.dialog.geometry("500x400")
        self.dialog.resizable(False, False)
        self.dialog.transient(self.parent)
        self.dialog.grab_set()

        # 居中显示
        self.center_dialog()

        # 创建界面
        self.create_widgets()

        # 加载现有数据
        self.load_current_data()

    def center_dialog(self):
        """居中显示对话框"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (400 // 2)
        self.dialog.geometry(f"500x400+{x}+{y}")

    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = tk.Frame(self.dialog, padx=20, pady=20)
        main_frame.pack(fill='both', expand=True)

        # 标题
        title_label = tk.Label(
            main_frame,
            text=f"编辑实例: {self.instance_name}",
            font=('Arial', 14, 'bold')
        )
        title_label.pack(pady=(0, 20))

        # 基本信息框架
        info_frame = tk.LabelFrame(main_frame, text="基本信息", padx=10, pady=10)
        info_frame.pack(fill='x', pady=(0, 10))

        # 显示名称
        tk.Label(info_frame, text="显示名称:").grid(row=0, column=0, sticky='w', pady=5)
        self.display_name_var = tk.StringVar()
        self.display_name_entry = tk.Entry(info_frame, textvariable=self.display_name_var, width=40)
        self.display_name_entry.grid(row=0, column=1, sticky='ew', padx=(10, 0), pady=5)

        # 描述
        tk.Label(info_frame, text="描述:").grid(row=1, column=0, sticky='w', pady=5)
        self.description_var = tk.StringVar()
        self.description_entry = tk.Entry(info_frame, textvariable=self.description_var, width=40)
        self.description_entry.grid(row=1, column=1, sticky='ew', padx=(10, 0), pady=5)

        # 图标
        tk.Label(info_frame, text="图标:").grid(row=2, column=0, sticky='w', pady=5)
        icon_frame = tk.Frame(info_frame)
        icon_frame.grid(row=2, column=1, sticky='ew', padx=(10, 0), pady=5)

        self.icon_var = tk.StringVar()
        self.icon_combo = ttk.Combobox(icon_frame, textvariable=self.icon_var, width=30)
        self.icon_combo.pack(side='left', fill='x', expand=True)

        # 加载图标列表
        self.load_icon_list()

        info_frame.columnconfigure(1, weight=1)

        # 启动设置框架
        startup_frame = tk.LabelFrame(main_frame, text="启动设置", padx=10, pady=10)
        startup_frame.pack(fill='x', pady=(0, 10))

        # 主页
        tk.Label(startup_frame, text="主页:").grid(row=0, column=0, sticky='w', pady=5)
        self.homepage_var = tk.StringVar()
        self.homepage_entry = tk.Entry(startup_frame, textvariable=self.homepage_var, width=40)
        self.homepage_entry.grid(row=0, column=1, sticky='ew', padx=(10, 0), pady=5)

        # 窗口设置
        tk.Label(startup_frame, text="窗口大小:").grid(row=1, column=0, sticky='w', pady=5)
        window_frame = tk.Frame(startup_frame)
        window_frame.grid(row=1, column=1, sticky='ew', padx=(10, 0), pady=5)

        self.width_var = tk.StringVar()
        self.height_var = tk.StringVar()

        tk.Label(window_frame, text="宽度:").pack(side='left')
        width_entry = tk.Entry(window_frame, textvariable=self.width_var, width=8)
        width_entry.pack(side='left', padx=(5, 10))

        tk.Label(window_frame, text="高度:").pack(side='left')
        height_entry = tk.Entry(window_frame, textvariable=self.height_var, width=8)
        height_entry.pack(side='left', padx=(5, 0))

        startup_frame.columnconfigure(1, weight=1)

        # 按钮框架
        button_frame = tk.Frame(main_frame)
        button_frame.pack(fill='x', pady=(20, 0))

        # 取消按钮
        cancel_btn = tk.Button(
            button_frame,
            text="取消",
            command=self.cancel,
            width=15,
            height=2,
            font=('Arial', 10)
        )
        cancel_btn.pack(side='right', padx=(10, 0))

        # 保存按钮
        save_btn = tk.Button(
            button_frame,
            text="保存",
            command=self.save_changes,
            bg='#4CAF50',
            fg='white',
            width=15,
            height=2,
            font=('Arial', 10, 'bold')
        )
        save_btn.pack(side='right')

    def load_icon_list(self):
        """加载图标列表"""
        try:
            icons = self.icon_manager.get_available_icons()
            self.icon_combo['values'] = icons
        except:
            self.icon_combo['values'] = ['default', 'chrome', 'work', 'personal', 'shopping']

    def load_current_data(self):
        """加载当前实例数据"""
        if not self.current_config:
            return

        # 基本信息
        self.display_name_var.set(self.current_config.get('display_name', ''))
        self.description_var.set(self.current_config.get('description', ''))
        self.icon_var.set(self.current_config.get('icon', 'default'))

        # 启动设置
        startup_options = self.current_config.get('startup_options', {})
        self.homepage_var.set(startup_options.get('homepage', ''))

        # 窗口设置
        window_settings = self.current_config.get('window_settings', {})
        self.width_var.set(str(window_settings.get('width', 1200)))
        self.height_var.set(str(window_settings.get('height', 800)))

    def validate_input(self):
        """验证输入"""
        # 验证显示名称
        display_name = self.display_name_var.get().strip()
        if not display_name:
            messagebox.showerror("错误", "请输入显示名称")
            return False

        # 验证窗口大小
        try:
            width = int(self.width_var.get())
            height = int(self.height_var.get())
            if width < 400 or height < 300:
                messagebox.showerror("错误", "窗口大小不能小于 400x300")
                return False
        except ValueError:
            messagebox.showerror("错误", "窗口大小必须是数字")
            return False

        # 验证主页URL
        homepage = self.homepage_var.get().strip()
        if homepage and not (homepage.startswith('http://') or homepage.startswith('https://')):
            messagebox.showerror("错误", "主页URL必须以 http:// 或 https:// 开头")
            return False

        return True

    def save_changes(self):
        """保存修改"""
        if not self.validate_input():
            return

        try:
            # 构建新配置
            new_config = self.current_config.copy()

            # 更新基本信息
            new_config['display_name'] = self.display_name_var.get().strip()
            new_config['description'] = self.description_var.get().strip()
            new_config['icon'] = self.icon_var.get()

            # 更新启动设置
            if 'startup_options' not in new_config:
                new_config['startup_options'] = {}

            new_config['startup_options']['homepage'] = self.homepage_var.get().strip()

            # 更新窗口设置
            if 'window_settings' not in new_config:
                new_config['window_settings'] = {}

            new_config['window_settings']['width'] = int(self.width_var.get())
            new_config['window_settings']['height'] = int(self.height_var.get())

            # 保存配置
            success = self.browser_manager.update_instance(self.instance_name, new_config)

            if success:
                messagebox.showinfo("成功", f"实例 '{self.instance_name}' 配置已更新")

                if self.on_success:
                    self.on_success()

                self.dialog.destroy()
            else:
                messagebox.showerror("错误", "保存配置失败")

        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {e}")

    def cancel(self):
        """取消"""
        self.dialog.destroy()


class CopyInstanceDialog:
    """复制实例对话框"""

    def __init__(self, parent, source_instance_name, on_success=None):
        self.parent = parent
        self.source_instance_name = source_instance_name
        self.on_success = on_success
        self.result = None

        self.browser_manager = BrowserManager()
        self.icon_manager = IconManager()

        # 加载源实例配置
        self.load_source_config()

        if self.source_config:
            self.create_dialog()
        else:
            messagebox.showerror("错误", f"无法加载源实例配置: {source_instance_name}")

    def load_source_config(self):
        """加载源实例配置"""
        try:
            instances = self.browser_manager.list_instances()
            self.source_config = None

            for instance in instances:
                if instance['name'] == self.source_instance_name:
                    self.source_config = instance['config'].copy()
                    break

            if not self.source_config:
                print(f"❌ 未找到源实例配置: {self.source_instance_name}")

        except Exception as e:
            print(f"❌ 加载源实例配置失败: {e}")
            self.source_config = None

    def create_dialog(self):
        """创建对话框"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title(f"复制实例 - {self.source_instance_name}")
        self.dialog.geometry("500x350")
        self.dialog.resizable(False, False)
        self.dialog.transient(self.parent)
        self.dialog.grab_set()

        # 居中显示
        self.center_dialog()

        # 创建界面
        self.create_widgets()

        # 加载默认数据
        self.load_default_data()

    def center_dialog(self):
        """居中显示对话框"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (350 // 2)
        self.dialog.geometry(f"500x350+{x}+{y}")

    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = tk.Frame(self.dialog, padx=20, pady=20)
        main_frame.pack(fill='both', expand=True)

        # 标题
        title_label = tk.Label(
            main_frame,
            text=f"复制实例: {self.source_instance_name}",
            font=('Arial', 14, 'bold')
        )
        title_label.pack(pady=(0, 20))

        # 新实例信息框架
        info_frame = tk.LabelFrame(main_frame, text="新实例信息", padx=10, pady=10)
        info_frame.pack(fill='x', pady=(0, 10))

        # 实例名称
        tk.Label(info_frame, text="实例名称:").grid(row=0, column=0, sticky='w', pady=5)
        self.instance_name_var = tk.StringVar()
        self.instance_name_entry = tk.Entry(info_frame, textvariable=self.instance_name_var, width=40)
        self.instance_name_entry.grid(row=0, column=1, sticky='ew', padx=(10, 0), pady=5)

        # 显示名称
        tk.Label(info_frame, text="显示名称:").grid(row=1, column=0, sticky='w', pady=5)
        self.display_name_var = tk.StringVar()
        self.display_name_entry = tk.Entry(info_frame, textvariable=self.display_name_var, width=40)
        self.display_name_entry.grid(row=1, column=1, sticky='ew', padx=(10, 0), pady=5)

        # 描述
        tk.Label(info_frame, text="描述:").grid(row=2, column=0, sticky='w', pady=5)
        self.description_var = tk.StringVar()
        self.description_entry = tk.Entry(info_frame, textvariable=self.description_var, width=40)
        self.description_entry.grid(row=2, column=1, sticky='ew', padx=(10, 0), pady=5)

        # 图标
        tk.Label(info_frame, text="图标:").grid(row=3, column=0, sticky='w', pady=5)
        self.icon_var = tk.StringVar()
        self.icon_combo = ttk.Combobox(info_frame, textvariable=self.icon_var, width=37)
        self.icon_combo.grid(row=3, column=1, sticky='ew', padx=(10, 0), pady=5)

        # 加载图标列表
        self.load_icon_list()

        info_frame.columnconfigure(1, weight=1)

        # 按钮框架
        button_frame = tk.Frame(main_frame)
        button_frame.pack(fill='x', pady=(20, 0))

        # 取消按钮
        cancel_btn = tk.Button(
            button_frame,
            text="取消",
            command=self.cancel,
            width=15,
            height=2,
            font=('Arial', 10)
        )
        cancel_btn.pack(side='right', padx=(10, 0))

        # 复制按钮
        copy_btn = tk.Button(
            button_frame,
            text="复制",
            command=self.copy_instance,
            bg='#2196F3',
            fg='white',
            width=15,
            height=2,
            font=('Arial', 10, 'bold')
        )
        copy_btn.pack(side='right')

    def load_icon_list(self):
        """加载图标列表"""
        try:
            icons = self.icon_manager.get_available_icons()
            self.icon_combo['values'] = icons
        except:
            self.icon_combo['values'] = ['default', 'chrome', 'work', 'personal', 'shopping']

    def load_default_data(self):
        """加载默认数据"""
        if not self.source_config:
            return

        # 生成默认的新实例名称
        base_name = f"{self.source_instance_name}_copy"
        new_name = base_name
        counter = 1

        # 检查名称是否已存在
        existing_instances = [inst['name'] for inst in self.browser_manager.list_instances()]
        while new_name in existing_instances:
            new_name = f"{base_name}_{counter}"
            counter += 1

        self.instance_name_var.set(new_name)

        # 复制源实例的信息并修改
        source_display_name = self.source_config.get('display_name', self.source_instance_name)
        self.display_name_var.set(f"{source_display_name} - 副本")

        source_description = self.source_config.get('description', '')
        self.description_var.set(f"复制自 {self.source_instance_name}: {source_description}")

        self.icon_var.set(self.source_config.get('icon', 'default'))

    def validate_input(self):
        """验证输入"""
        # 验证实例名称
        instance_name = self.instance_name_var.get().strip()
        if not instance_name:
            messagebox.showerror("错误", "请输入实例名称")
            return False

        # 检查名称格式
        import re
        if not re.match(r'^[a-zA-Z0-9_-]+$', instance_name):
            messagebox.showerror("错误", "实例名称只能包含字母、数字、下划线和连字符")
            return False

        # 检查名称是否已存在
        existing_instances = [inst['name'] for inst in self.browser_manager.list_instances()]
        if instance_name in existing_instances:
            messagebox.showerror("错误", f"实例名称 '{instance_name}' 已存在")
            return False

        # 验证显示名称
        display_name = self.display_name_var.get().strip()
        if not display_name:
            messagebox.showerror("错误", "请输入显示名称")
            return False

        return True

    def copy_instance(self):
        """复制实例"""
        if not self.validate_input():
            return

        try:
            # 构建新实例配置
            new_config = {
                'instance_name': self.instance_name_var.get().strip(),
                'display_name': self.display_name_var.get().strip(),
                'description': self.description_var.get().strip(),
                'icon': self.icon_var.get(),
                'startup_options': self.source_config.get('startup_options', {}),
                'window_settings': self.source_config.get('window_settings', {}),
                'chrome_args': self.source_config.get('chrome_args', [])
            }

            # 执行复制
            success = self.browser_manager.copy_instance(
                self.source_instance_name,
                new_config['instance_name'],
                new_config
            )

            if success:
                messagebox.showinfo(
                    "复制成功",
                    f"实例 '{self.source_instance_name}' 已复制为 '{new_config['instance_name']}'"
                )

                if self.on_success:
                    self.on_success()

                self.dialog.destroy()
            else:
                messagebox.showerror("错误", "复制实例失败")

        except Exception as e:
            messagebox.showerror("错误", f"复制实例失败: {e}")

    def cancel(self):
        """取消"""
        self.dialog.destroy()
