#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合功能测试脚本
测试所有功能的正确性和跨电脑可移植性
"""

import os
import sys
import json
import time
import shutil
import subprocess
from pathlib import Path
from datetime import datetime


class ComprehensiveTest:
    """综合功能测试器"""
    
    def __init__(self, base_dir=None):
        """初始化测试器"""
        if base_dir is None:
            self.base_dir = Path(__file__).parent.parent
        else:
            self.base_dir = Path(base_dir)
            
        self.test_results = []
        self.test_instances = []
        
    def log_test(self, test_name, success, message=""):
        """记录测试结果"""
        result = {
            "test_name": test_name,
            "success": success,
            "message": message,
            "timestamp": datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        status = "[通过]" if success else "[失败]"
        print(f"{status} {test_name}: {message}")
        
    def test_environment(self):
        """测试环境检查"""
        print("测试环境检查...")
        
        # 检查Python版本
        python_version = sys.version_info
        if python_version >= (3, 7):
            self.log_test("Python版本检查", True, f"Python {python_version.major}.{python_version.minor}")
        else:
            self.log_test("Python版本检查", False, f"需要Python 3.7+，当前版本: {python_version.major}.{python_version.minor}")
            
        # 检查Chrome Portable
        chrome_exe = self.base_dir / "GoogleChromePortable" / "App" / "Chrome-bin" / "chrome.exe"
        if chrome_exe.exists():
            self.log_test("Chrome Portable检查", True, f"找到Chrome: {chrome_exe}")
        else:
            self.log_test("Chrome Portable检查", False, f"未找到Chrome: {chrome_exe}")
            
        # 检查必要目录
        required_dirs = ["tools", "instances", "icons", "scripts"]
        for dir_name in required_dirs:
            dir_path = self.base_dir / dir_name
            if dir_path.exists():
                self.log_test(f"目录检查 - {dir_name}", True, f"目录存在: {dir_path}")
            else:
                self.log_test(f"目录检查 - {dir_name}", False, f"目录不存在: {dir_path}")
                
    def test_browser_manager(self):
        """测试浏览器实例管理功能"""
        print("\n测试浏览器实例管理...")

        # 测试创建实例
        test_instance = "test_instance_" + str(int(time.time()))
        self.test_instances.append(test_instance)
        
        try:
            cmd = [
                sys.executable, "tools/browser_manager.py", "create", test_instance,
                "--display-name", "测试实例",
                "--description", "用于功能测试的实例",
                "--homepage", "https://www.google.com"
            ]
            result = subprocess.run(cmd, cwd=self.base_dir, capture_output=True, text=True)
            
            if result.returncode == 0:
                self.log_test("创建实例", True, f"成功创建实例: {test_instance}")
            else:
                self.log_test("创建实例", False, f"创建失败: {result.stderr}")
                
        except Exception as e:
            self.log_test("创建实例", False, f"异常: {str(e)}")
            
        # 测试列出实例
        try:
            cmd = [sys.executable, "tools/browser_manager.py", "list"]
            result = subprocess.run(cmd, cwd=self.base_dir, capture_output=True, text=True)
            
            if result.returncode == 0 and test_instance in result.stdout:
                self.log_test("列出实例", True, "成功列出实例")
            else:
                self.log_test("列出实例", False, "列出实例失败或未找到测试实例")
                
        except Exception as e:
            self.log_test("列出实例", False, f"异常: {str(e)}")
            
    def test_icon_manager(self):
        """测试图标管理功能"""
        print("\n测试图标管理...")

        # 测试生成图标
        try:
            cmd = [sys.executable, "tools/icon_manager.py", "generate", "--theme", "work"]
            result = subprocess.run(cmd, cwd=self.base_dir, capture_output=True, text=True)
            
            if result.returncode == 0:
                self.log_test("生成图标", True, "成功生成工作主题图标")
            else:
                self.log_test("生成图标", False, f"生成失败: {result.stderr}")
                
        except Exception as e:
            self.log_test("生成图标", False, f"异常: {str(e)}")
            
        # 测试列出图标
        try:
            cmd = [sys.executable, "tools/icon_manager.py", "list"]
            result = subprocess.run(cmd, cwd=self.base_dir, capture_output=True, text=True)
            
            if result.returncode == 0:
                self.log_test("列出图标", True, "成功列出可用图标")
            else:
                self.log_test("列出图标", False, f"列出失败: {result.stderr}")
                
        except Exception as e:
            self.log_test("列出图标", False, f"异常: {str(e)}")
            
        # 测试设置图标
        if self.test_instances:
            try:
                test_instance = self.test_instances[0]
                cmd = [sys.executable, "tools/icon_manager.py", "set", test_instance, "work"]
                result = subprocess.run(cmd, cwd=self.base_dir, capture_output=True, text=True)
                
                if result.returncode == 0:
                    self.log_test("设置图标", True, f"成功为实例 {test_instance} 设置图标")
                else:
                    self.log_test("设置图标", False, f"设置失败: {result.stderr}")
                    
            except Exception as e:
                self.log_test("设置图标", False, f"异常: {str(e)}")
                
    def test_launcher_generator(self):
        """测试启动脚本生成功能"""
        print("\n测试启动脚本生成...")

        # 测试生成启动脚本
        if self.test_instances:
            try:
                test_instance = self.test_instances[0]
                cmd = [sys.executable, "tools/launcher_generator.py", "generate", test_instance]
                result = subprocess.run(cmd, cwd=self.base_dir, capture_output=True, text=True)
                
                if result.returncode == 0:
                    self.log_test("生成启动脚本", True, f"成功为实例 {test_instance} 生成启动脚本")
                    
                    # 检查生成的文件
                    instance_dir = self.base_dir / "instances" / test_instance
                    python_script = instance_dir / f"chrome_{test_instance}.py"
                    bat_script = instance_dir / f"启动_{test_instance}.bat"
                    
                    if python_script.exists():
                        self.log_test("Python启动脚本", True, f"Python脚本存在: {python_script}")
                    else:
                        self.log_test("Python启动脚本", False, f"Python脚本不存在: {python_script}")
                        
                    if bat_script.exists():
                        self.log_test("批处理启动脚本", True, f"批处理脚本存在: {bat_script}")
                    else:
                        self.log_test("批处理启动脚本", False, f"批处理脚本不存在: {bat_script}")
                        
                else:
                    self.log_test("生成启动脚本", False, f"生成失败: {result.stderr}")
                    
            except Exception as e:
                self.log_test("生成启动脚本", False, f"异常: {str(e)}")
                
        # 测试全局启动器
        try:
            cmd = [sys.executable, "tools/launcher_generator.py", "global"]
            result = subprocess.run(cmd, cwd=self.base_dir, capture_output=True, text=True)
            
            if result.returncode == 0:
                self.log_test("全局启动器", True, "成功生成全局启动器")
            else:
                self.log_test("全局启动器", False, f"生成失败: {result.stderr}")
                
        except Exception as e:
            self.log_test("全局启动器", False, f"异常: {str(e)}")
            
    def test_batch_configurator(self):
        """测试批量配置功能"""
        print("\n测试批量配置...")

        # 测试列出配置模板
        try:
            cmd = [sys.executable, "tools/batch_configurator.py", "list"]
            result = subprocess.run(cmd, cwd=self.base_dir, capture_output=True, text=True)
            
            if result.returncode == 0:
                self.log_test("列出配置模板", True, "成功列出配置模板")
            else:
                self.log_test("列出配置模板", False, f"列出失败: {result.stderr}")
                
        except Exception as e:
            self.log_test("列出配置模板", False, f"异常: {str(e)}")
            
        # 测试预览配置模板
        try:
            cmd = [sys.executable, "tools/batch_configurator.py", "preview", "basic_set.json"]
            result = subprocess.run(cmd, cwd=self.base_dir, capture_output=True, text=True)
            
            if result.returncode == 0:
                self.log_test("预览配置模板", True, "成功预览basic_set.json")
            else:
                self.log_test("预览配置模板", False, f"预览失败: {result.stderr}")
                
        except Exception as e:
            self.log_test("预览配置模板", False, f"异常: {str(e)}")
            
    def test_template_manager(self):
        """测试模板管理功能"""
        print("\n测试模板管理...")

        # 测试列出模板
        try:
            cmd = [sys.executable, "tools/template_manager.py", "list"]
            result = subprocess.run(cmd, cwd=self.base_dir, capture_output=True, text=True)
            
            if result.returncode == 0:
                self.log_test("列出模板", True, "成功列出所有模板")
            else:
                self.log_test("列出模板", False, f"列出失败: {result.stderr}")
                
        except Exception as e:
            self.log_test("列出模板", False, f"异常: {str(e)}")
            
    def test_isolation_validator(self):
        """测试数据隔离验证功能"""
        print("\n测试数据隔离验证...")

        # 测试验证数据隔离
        try:
            cmd = [sys.executable, "tools/isolation_validator.py", "validate"]
            result = subprocess.run(cmd, cwd=self.base_dir, capture_output=True, text=True)
            
            if result.returncode == 0:
                self.log_test("数据隔离验证", True, "数据隔离验证通过")
            else:
                self.log_test("数据隔离验证", False, f"验证失败: {result.stderr}")
                
        except Exception as e:
            self.log_test("数据隔离验证", False, f"异常: {str(e)}")
            
    def test_file_structure(self):
        """测试文件结构完整性"""
        print("\n测试文件结构...")

        # 检查关键文件
        key_files = [
            "tools/browser_manager.py",
            "tools/icon_manager.py",
            "tools/launcher_generator.py",
            "tools/batch_configurator.py",
            "tools/template_manager.py",
            "tools/isolation_validator.py",
            "scripts/launch_browser.py",
            "README.md",
            "docs/data_isolation.md"
        ]
        
        for file_path in key_files:
            full_path = self.base_dir / file_path
            if full_path.exists():
                self.log_test(f"文件检查 - {file_path}", True, "文件存在")
            else:
                self.log_test(f"文件检查 - {file_path}", False, "文件不存在")
                
        # 检查配置模板目录
        templates_dir = self.base_dir / "tools" / "config_templates"
        if templates_dir.exists():
            template_files = list(templates_dir.glob("*.json"))
            if len(template_files) > 0:
                self.log_test("配置模板", True, f"找到 {len(template_files)} 个配置模板")
            else:
                self.log_test("配置模板", False, "未找到配置模板文件")
        else:
            self.log_test("配置模板目录", False, "配置模板目录不存在")
            
    def cleanup_test_instances(self):
        """清理测试实例"""
        print("\n清理测试实例...")

        for test_instance in self.test_instances:
            try:
                cmd = [sys.executable, "tools/browser_manager.py", "delete", test_instance]
                result = subprocess.run(cmd, cwd=self.base_dir, capture_output=True, text=True)
                
                if result.returncode == 0:
                    self.log_test(f"清理实例 - {test_instance}", True, "成功删除测试实例")
                else:
                    self.log_test(f"清理实例 - {test_instance}", False, f"删除失败: {result.stderr}")
                    
            except Exception as e:
                self.log_test(f"清理实例 - {test_instance}", False, f"异常: {str(e)}")
                
    def generate_report(self):
        """生成测试报告"""
        print("\n生成测试报告...")

        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        report = {
            "test_summary": {
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "failed_tests": failed_tests,
                "success_rate": f"{success_rate:.1f}%"
            },
            "test_results": self.test_results,
            "generated_at": datetime.now().isoformat()
        }
        
        # 保存报告到文件
        report_file = self.base_dir / "test_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
            
        print(f"\n测试报告")
        print("=" * 50)
        print(f"总测试数: {total_tests}")
        print(f"通过测试: {passed_tests}")
        print(f"失败测试: {failed_tests}")
        print(f"成功率: {success_rate:.1f}%")
        print(f"报告文件: {report_file}")

        if failed_tests > 0:
            print("\n失败的测试:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"  - {result['test_name']}: {result['message']}")
                    
        return success_rate >= 80  # 80%以上通过率认为测试成功
        
    def run_all_tests(self):
        """运行所有测试"""
        print("开始综合功能测试...")
        print("=" * 50)

        # 运行各项测试
        self.test_environment()
        self.test_file_structure()
        self.test_browser_manager()
        self.test_icon_manager()
        self.test_launcher_generator()
        self.test_batch_configurator()
        self.test_template_manager()
        self.test_isolation_validator()

        # 清理测试数据
        self.cleanup_test_instances()

        # 生成报告
        success = self.generate_report()

        if success:
            print("\n综合测试通过！项目功能正常。")
        else:
            print("\n综合测试失败！请检查失败的测试项目。")
            
        return success


def main():
    """命令行接口"""
    import argparse
    
    parser = argparse.ArgumentParser(description="综合功能测试脚本")
    parser.add_argument("--base-dir", help="项目根目录路径")
    
    args = parser.parse_args()
    
    tester = ComprehensiveTest(args.base_dir)
    success = tester.run_all_tests()
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
