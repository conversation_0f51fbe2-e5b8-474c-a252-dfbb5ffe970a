#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查现代化界面状态和功能
"""

import sys
import os
import json
import time
from pathlib import Path
from urllib.request import urlopen, Request
from urllib.error import URLError, HTTPError
from urllib.parse import urlencode

class InterfaceChecker:
    """界面检查器"""
    
    def __init__(self, base_url="http://127.0.0.1:8000"):
        self.base_url = base_url
        self.results = []
    
    def log_result(self, test_name, success, message=""):
        """记录检查结果"""
        status = "✅" if success else "❌"
        result = f"{status} {test_name}"
        if message:
            result += f" - {message}"
        print(result)
        self.results.append((test_name, success, message))
    
    def check_server_running(self):
        """检查服务器是否运行"""
        try:
            response = urlopen(self.base_url, timeout=3)
            if response.getcode() == 200:
                self.log_result("服务器状态", True, "运行正常")
                return True
            else:
                self.log_result("服务器状态", False, f"HTTP {response.getcode()}")
                return False
        except Exception as e:
            self.log_result("服务器状态", False, f"无法连接: {str(e)}")
            return False
    
    def check_main_page(self):
        """检查主页面"""
        try:
            response = urlopen(f"{self.base_url}/", timeout=5)
            if response.getcode() == 200:
                content = response.read().decode('utf-8')
                
                # 检查关键元素
                key_elements = [
                    "浏览器管理",
                    "创建浏览器",
                    "批量操作",
                    "data-table",
                    "modal",
                    "sidebar"
                ]
                
                missing = []
                for element in key_elements:
                    if element not in content:
                        missing.append(element)
                
                if not missing:
                    self.log_result("主页面内容", True, "所有关键元素存在")
                else:
                    self.log_result("主页面内容", False, f"缺少: {', '.join(missing)}")
            else:
                self.log_result("主页面内容", False, f"HTTP {response.getcode()}")
        except Exception as e:
            self.log_result("主页面内容", False, str(e))
    
    def check_static_files(self):
        """检查静态文件"""
        static_files = [
            "/static/css/style.css",
            "/static/js/app.js"
        ]
        
        for file_path in static_files:
            try:
                response = urlopen(f"{self.base_url}{file_path}", timeout=5)
                if response.getcode() == 200:
                    size = len(response.read())
                    self.log_result(f"静态文件 {file_path}", True, f"{size} 字节")
                else:
                    self.log_result(f"静态文件 {file_path}", False, f"HTTP {response.getcode()}")
            except Exception as e:
                self.log_result(f"静态文件 {file_path}", False, str(e))
    
    def check_api_endpoints(self):
        """检查API端点"""
        api_endpoints = [
            "/api/instances",
            "/api/icons", 
            "/api/groups"
        ]
        
        for endpoint in api_endpoints:
            try:
                response = urlopen(f"{self.base_url}{endpoint}", timeout=5)
                if response.getcode() == 200:
                    data = json.loads(response.read().decode('utf-8'))
                    if data.get('success'):
                        count = len(data.get('data', []))
                        self.log_result(f"API {endpoint}", True, f"返回 {count} 项数据")
                    else:
                        self.log_result(f"API {endpoint}", False, data.get('error', '未知错误'))
                else:
                    self.log_result(f"API {endpoint}", False, f"HTTP {response.getcode()}")
            except Exception as e:
                self.log_result(f"API {endpoint}", False, str(e))
    
    def check_file_structure(self):
        """检查文件结构"""
        required_files = [
            "templates/index.html",
            "static/css/style.css", 
            "static/js/app.js",
            "tools/browser_manager.py",
            "tools/icon_manager.py",
            "启动现代化界面.py"
        ]
        
        for file_path in required_files:
            if Path(file_path).exists():
                size = Path(file_path).stat().st_size
                self.log_result(f"文件 {file_path}", True, f"{size} 字节")
            else:
                self.log_result(f"文件 {file_path}", False, "文件不存在")
    
    def check_browser_manager(self):
        """检查浏览器管理器"""
        try:
            sys.path.append(str(Path(__file__).parent / "tools"))
            from browser_manager import BrowserManager
            
            manager = BrowserManager()
            instances = manager.list_instances()
            
            self.log_result("BrowserManager", True, f"找到 {len(instances)} 个实例")
            
            # 检查关键方法
            methods = ['create_instance', 'delete_instance', 'launch_instance', 
                      'is_instance_running', 'stop_instance', 'update_instance']
            
            for method in methods:
                if hasattr(manager, method):
                    self.log_result(f"方法 {method}", True, "存在")
                else:
                    self.log_result(f"方法 {method}", False, "不存在")
                    
        except Exception as e:
            self.log_result("BrowserManager", False, str(e))
    
    def run_all_checks(self):
        """运行所有检查"""
        print("=" * 60)
        print("🔍 现代化界面状态检查")
        print("=" * 60)
        
        print("🌐 服务器检查:")
        server_running = self.check_server_running()
        
        if server_running:
            print("\n📄 页面检查:")
            self.check_main_page()
            self.check_static_files()
            
            print("\n🔌 API检查:")
            self.check_api_endpoints()
        else:
            print("\n⚠️  服务器未运行，跳过在线检查")
            print("请运行: python 启动现代化界面.py")
        
        print("\n📁 文件检查:")
        self.check_file_structure()
        
        print("\n🔧 组件检查:")
        self.check_browser_manager()
        
        # 统计结果
        print("\n" + "=" * 60)
        print("📊 检查结果统计:")
        print("=" * 60)
        
        total = len(self.results)
        passed = sum(1 for _, success, _ in self.results if success)
        failed = total - passed
        
        print(f"总检查项: {total}")
        print(f"通过: {passed}")
        print(f"失败: {failed}")
        print(f"成功率: {passed/total*100:.1f}%")
        
        if failed > 0:
            print("\n❌ 失败项目:")
            for name, success, message in self.results:
                if not success:
                    print(f"  - {name}: {message}")
        
        print("\n" + "=" * 60)
        if failed == 0:
            print("🎉 所有检查通过！界面运行完美！")
        elif failed <= 2:
            print("✅ 界面基本正常，有少量问题")
        else:
            print("⚠️  发现多个问题，需要修复")
        print("=" * 60)
        
        return failed == 0

def main():
    """主函数"""
    checker = InterfaceChecker()
    success = checker.run_all_checks()
    
    if not success:
        print("\n💡 修复建议:")
        print("1. 确保服务器正在运行: python 启动现代化界面.py")
        print("2. 检查文件是否完整")
        print("3. 查看服务器日志排查错误")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
