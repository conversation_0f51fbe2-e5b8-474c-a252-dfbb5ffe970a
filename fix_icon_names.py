#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复图标名称 - 移除配置中的.ico后缀
"""

import sys
import os
import json
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))
sys.path.append(str(Path(__file__).parent / "tools"))

def fix_icon_names():
    """修复所有实例配置中的图标名称"""
    print("🔧 修复图标名称...")
    
    try:
        from browser_manager import BrowserManager
        
        manager = BrowserManager()
        instances_dir = Path(__file__).parent / "instances"
        
        if not instances_dir.exists():
            print("❌ 实例目录不存在")
            return False
        
        fixed_count = 0
        total_count = 0
        
        for instance_dir in instances_dir.iterdir():
            if instance_dir.is_dir():
                config_path = instance_dir / "config.json"
                if config_path.exists():
                    total_count += 1
                    
                    try:
                        # 读取配置
                        with open(config_path, 'r', encoding='utf-8') as f:
                            config = json.load(f)
                        
                        # 检查图标名称
                        icon_name = config.get('icon', 'default')
                        original_icon = icon_name
                        
                        # 移除.ico后缀
                        if icon_name.endswith('.ico'):
                            icon_name = icon_name[:-4]
                            config['icon'] = icon_name
                            
                            # 保存修复后的配置
                            with open(config_path, 'w', encoding='utf-8') as f:
                                json.dump(config, f, indent=2, ensure_ascii=False)
                            
                            print(f"✅ 修复实例 {instance_dir.name}: {original_icon} -> {icon_name}")
                            fixed_count += 1
                        else:
                            print(f"✓ 实例 {instance_dir.name}: {icon_name} (无需修复)")
                            
                    except Exception as e:
                        print(f"❌ 修复实例 {instance_dir.name} 失败: {e}")
        
        print(f"\n📊 修复完成: {fixed_count}/{total_count} 个实例需要修复")
        return True
        
    except Exception as e:
        print(f"❌ 修复图标名称失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_icons():
    """验证图标文件是否存在"""
    print("\n🔍 验证图标文件...")
    
    try:
        from browser_manager import BrowserManager
        
        manager = BrowserManager()
        instances = manager.list_instances()
        icons_dir = Path(__file__).parent / "icons"
        
        missing_icons = []
        
        for instance in instances:
            config = instance.get('config', {})
            icon_name = config.get('icon', 'default')
            
            # 确保没有.ico后缀
            if icon_name.endswith('.ico'):
                icon_name = icon_name[:-4]
            
            icon_path = icons_dir / f"{icon_name}.ico"
            
            if not icon_path.exists():
                missing_icons.append({
                    'instance': instance['name'],
                    'icon': icon_name,
                    'path': icon_path
                })
                print(f"⚠️ 实例 {instance['name']} 的图标文件不存在: {icon_name}.ico")
            else:
                print(f"✅ 实例 {instance['name']} 的图标文件存在: {icon_name}.ico")
        
        if missing_icons:
            print(f"\n⚠️ 发现 {len(missing_icons)} 个缺失的图标文件")
            print("建议:")
            print("1. 使用默认图标替换")
            print("2. 生成主题图标")
            print("3. 手动添加图标文件")
            
            # 提供修复选项
            choice = input("\n是否自动修复缺失的图标？(y/n): ").lower().strip()
            if choice == 'y':
                return fix_missing_icons(missing_icons)
        else:
            print("\n✅ 所有图标文件都存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证图标文件失败: {e}")
        return False

def fix_missing_icons(missing_icons):
    """修复缺失的图标"""
    print("\n🔧 修复缺失的图标...")
    
    try:
        from icon_manager import IconManager
        
        icon_manager = IconManager()
        fixed_count = 0
        
        for missing in missing_icons:
            instance_name = missing['instance']
            icon_name = missing['icon']
            
            # 尝试生成主题图标
            if icon_name in icon_manager.color_themes:
                print(f"🎨 生成主题图标: {icon_name}")
                if icon_manager.generate_theme_icon(icon_name):
                    print(f"✅ 为实例 {instance_name} 生成了图标: {icon_name}")
                    fixed_count += 1
                else:
                    print(f"❌ 生成图标失败: {icon_name}")
            else:
                # 使用默认图标
                print(f"🔄 为实例 {instance_name} 设置默认图标")
                if icon_manager.set_instance_icon(instance_name, 'default'):
                    fixed_count += 1
        
        print(f"\n📊 修复完成: {fixed_count}/{len(missing_icons)} 个图标已修复")
        return True
        
    except Exception as e:
        print(f"❌ 修复缺失图标失败: {e}")
        return False

def test_display():
    """测试修复后的显示效果"""
    print("\n🧪 测试修复后的显示效果...")
    
    try:
        import tkinter as tk
        from PIL import Image, ImageTk
        from browser_manager import BrowserManager
        
        manager = BrowserManager()
        instances = manager.list_instances()
        
        if not instances:
            print("❌ 没有实例可供测试")
            return False
        
        # 创建测试窗口
        root = tk.Tk()
        root.title("修复后显示测试")
        root.geometry("600x400")
        
        # 创建滚动框架
        canvas = tk.Canvas(root)
        scrollbar = tk.Scrollbar(root, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 显示所有实例
        for i, instance in enumerate(instances):
            config = instance.get('config', {})
            display_name = config.get('display_name', instance['name'])
            description = config.get('description', '无描述')
            icon_name = config.get('icon', 'default')
            
            # 移除可能的.ico后缀
            if icon_name.endswith('.ico'):
                icon_name = icon_name[:-4]
            
            # 创建实例框架
            instance_frame = tk.Frame(scrollable_frame, relief='raised', bd=1, bg='white')
            instance_frame.pack(fill='x', padx=10, pady=5)
            
            # 图标
            icon_frame = tk.Frame(instance_frame, bg='white', width=60)
            icon_frame.pack(side='left', fill='y', padx=5)
            icon_frame.pack_propagate(False)
            
            try:
                icon_path = Path(__file__).parent / "icons" / f"{icon_name}.ico"
                if icon_path.exists():
                    img = Image.open(icon_path)
                    img = img.resize((48, 48), Image.Resampling.LANCZOS)
                    icon_photo = ImageTk.PhotoImage(img)
                    
                    icon_label = tk.Label(icon_frame, image=icon_photo, bg='white')
                    icon_label.image = icon_photo  # 保持引用
                    icon_label.pack(expand=True)
                else:
                    icon_label = tk.Label(icon_frame, text='🌐', font=('Arial', 24), bg='white')
                    icon_label.pack(expand=True)
            except:
                icon_label = tk.Label(icon_frame, text='❌', font=('Arial', 24), bg='white')
                icon_label.pack(expand=True)
            
            # 信息
            info_frame = tk.Frame(instance_frame, bg='white')
            info_frame.pack(side='left', fill='both', expand=True, padx=5)
            
            name_label = tk.Label(info_frame, text=display_name, font=('Arial', 12, 'bold'), bg='white', anchor='w')
            name_label.pack(fill='x', pady=(5, 0))
            
            desc_label = tk.Label(info_frame, text=description, font=('Arial', 9), bg='white', fg='gray', anchor='w')
            desc_label.pack(fill='x')
            
            icon_info_label = tk.Label(info_frame, text=f"图标: {icon_name}", font=('Arial', 8), bg='white', fg='blue', anchor='w')
            icon_info_label.pack(fill='x', pady=(0, 5))
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 5秒后自动关闭
        def close_window():
            root.destroy()
        
        root.after(5000, close_window)
        
        print("✅ 测试窗口已显示，5秒后自动关闭")
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ 显示测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🔧 图标名称修复工具")
    print("=" * 60)
    
    steps = [
        ("修复图标名称", fix_icon_names),
        ("验证图标文件", verify_icons),
        ("测试显示效果", test_display)
    ]
    
    for step_name, step_func in steps:
        print(f"\n{'='*15} {step_name} {'='*15}")
        try:
            if not step_func():
                print(f"❌ {step_name} 失败")
                break
        except Exception as e:
            print(f"❌ {step_name} 异常: {e}")
            break
    else:
        print("\n" + "=" * 60)
        print("🎉 图标修复完成！")
        print("=" * 60)
        print("\n✅ 修复内容:")
        print("• 移除配置中的.ico后缀")
        print("• 验证图标文件存在性")
        print("• 生成缺失的主题图标")
        print("• 测试显示效果")
        
        print("\n🚀 现在可以:")
        print("1. 启动GUI界面查看修复效果")
        print("2. 编辑实例配置测试功能")
        print("3. 复制实例验证图标显示")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"\n修复异常: {e}")
    
    print("\n按回车键退出...")
    input()
