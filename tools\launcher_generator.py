#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动脚本生成模块
为每个浏览器实例生成独立的启动脚本和快捷方式
"""

import os
import json
import subprocess
from pathlib import Path
from datetime import datetime


class LauncherGenerator:
    """启动脚本生成器"""
    
    def __init__(self, base_dir=None):
        """初始化生成器"""
        if base_dir is None:
            self.base_dir = Path(__file__).parent.parent
        else:
            self.base_dir = Path(base_dir)
            
        self.instances_dir = self.base_dir / "instances"
        self.icons_dir = self.base_dir / "icons"
        self.scripts_dir = self.base_dir / "scripts"
        
        # 确保目录存在
        self.scripts_dir.mkdir(exist_ok=True)
    
    def generate_python_launcher(self, instance_name, config):
        """生成Python启动脚本"""
        chrome_args = config.get("chrome_args", [])
        homepage = config.get("startup_options", {}).get("homepage", "")
        display_name = config.get('display_name', instance_name)
        
        # 构建完整的参数列表
        all_args = chrome_args.copy()
        if homepage:
            all_args.append(homepage)
        
        # 生成参数字符串
        args_lines = []
        for arg in all_args:
            args_lines.append(f'        "{arg}",')
        args_str = "\n".join(args_lines)
        
        # 生成启动脚本内容
        script_content = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
{display_name} 启动脚本
自动生成，请勿手动修改
实例: {instance_name}
生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
"""

import os
import sys
import subprocess
from pathlib import Path


def check_chrome_portable():
    """检查Chrome Portable是否存在"""
    script_dir = Path(__file__).parent
    base_dir = script_dir.parent.parent
    chrome_exe = base_dir / "GoogleChromePortable" / "App" / "Chrome-bin" / "chrome.exe"
    
    if not chrome_exe.exists():
        print(f"Chrome Portable不存在: {{chrome_exe}}")
        print("请确保GoogleChromePortable目录在项目根目录中")
        return False
    return True


def check_data_directory():
    """检查数据目录是否存在"""
    script_dir = Path(__file__).parent
    data_dir = script_dir / "Data" / "profile"
    
    if not data_dir.exists():
        print(f"数据目录不存在: {{data_dir}}")
        print("请重新创建实例或检查数据目录")
        return False
    return True


def main():
    """主启动函数"""
    print(f"启动 {display_name}...")
    
    # 检查依赖
    if not check_chrome_portable():
        input("按回车键退出...")
        return
    
    if not check_data_directory():
        input("按回车键退出...")
        return
    
    # 获取路径
    script_dir = Path(__file__).parent
    base_dir = script_dir.parent.parent
    chrome_exe = base_dir / "GoogleChromePortable" / "App" / "Chrome-bin" / "chrome.exe"
    user_data_dir = script_dir / "Data" / "profile"
    
    # 构建启动命令
    cmd = [
        str(chrome_exe),
        f"--user-data-dir={{user_data_dir}}",
{args_str}
    ]
    
    # 启动Chrome
    try:
        # 使用subprocess.Popen启动，不等待进程结束
        process = subprocess.Popen(cmd, 
                                 stdout=subprocess.DEVNULL, 
                                 stderr=subprocess.DEVNULL)
        print(f"{display_name} 启动成功 (PID: {{process.pid}})")

    except FileNotFoundError:
        print("Chrome可执行文件未找到")
        print("请检查GoogleChromePortable是否正确安装")
        input("按回车键退出...")
    except Exception as e:
        print(f"启动失败: {{e}}")
        input("按回车键退出...")


if __name__ == "__main__":
    main()
'''
        
        return script_content
    
    def generate_batch_launcher(self, instance_name, config):
        """生成批处理启动脚本（Windows）"""
        display_name = config.get('display_name', instance_name)

        batch_content = f'''@echo off
REM {display_name} 启动脚本
REM 自动生成，请勿手动修改
REM 实例: {instance_name}
REM 生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

title {display_name}
echo 启动 {display_name}...

REM 切换到脚本目录
cd /d "%~dp0"

REM 启动Python脚本
python chrome_{instance_name}.py

REM 如果Python脚本执行失败，暂停以显示错误信息
if errorlevel 1 (
    echo.
    echo 启动失败，请检查Python环境和Chrome Portable
    pause
)
'''

        return batch_content
    
    def generate_all_launchers(self, instance_name):
        """为指定实例生成所有类型的启动脚本"""
        instance_dir = self.instances_dir / instance_name
        config_path = instance_dir / "config.json"
        
        if not config_path.exists():
            print(f"实例配置不存在: {instance_name}")
            return False
        
        try:
            # 读取配置
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 生成Python启动脚本
            python_script = self.generate_python_launcher(instance_name, config)
            python_path = instance_dir / f"chrome_{instance_name}.py"
            
            with open(python_path, 'w', encoding='utf-8') as f:
                f.write(python_script)
            
            print(f"已生成Python启动脚本: {python_path}")

            # 生成批处理启动脚本（Windows）
            if os.name == 'nt':
                batch_script = self.generate_batch_launcher(instance_name, config)
                batch_path = instance_dir / f"启动_{instance_name}.bat"

                with open(batch_path, 'w', encoding='gbk') as f:
                    f.write(batch_script)

                print(f"已生成批处理启动脚本: {batch_path}")

            return True

        except Exception as e:
            print(f"生成启动脚本失败: {e}")
            return False
    
    def regenerate_all_instances(self):
        """重新生成所有实例的启动脚本"""
        if not self.instances_dir.exists():
            print("实例目录不存在")
            return 0

        success_count = 0
        instances = []

        # 收集所有实例
        for item in self.instances_dir.iterdir():
            if item.is_dir() and (item / "config.json").exists():
                instances.append(item.name)

        if not instances:
            print("没有找到实例")
            return 0

        print(f"重新生成 {len(instances)} 个实例的启动脚本...")

        for instance_name in instances:
            if self.generate_all_launchers(instance_name):
                success_count += 1

        print(f"成功重新生成 {success_count}/{len(instances)} 个实例的启动脚本")
        return success_count
    
    def create_global_launcher(self):
        """创建全局启动器，可以选择启动哪个实例"""
        launcher_content = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器实例全局启动器
可以选择启动任意实例
生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
"""

import os
import sys
import json
import subprocess
from pathlib import Path


def get_available_instances():
    """获取可用的实例列表"""
    script_dir = Path(__file__).parent
    instances_dir = script_dir.parent / "instances"
    
    instances = []
    if instances_dir.exists():
        for item in instances_dir.iterdir():
            if item.is_dir():
                config_path = item / "config.json"
                if config_path.exists():
                    try:
                        with open(config_path, 'r', encoding='utf-8') as f:
                            config = json.load(f)
                        instances.append({{
                            "name": item.name,
                            "display_name": config.get("display_name", item.name),
                            "description": config.get("description", ""),
                            "icon": config.get("icon", "default.ico")
                        }})
                    except Exception:
                        pass
    
    return instances


def launch_instance(instance_name):
    """启动指定实例"""
    script_dir = Path(__file__).parent
    instance_dir = script_dir.parent / "instances" / instance_name
    launcher_script = instance_dir / f"chrome_{{instance_name}}.py"
    
    if not launcher_script.exists():
        print(f"启动脚本不存在: {{launcher_script}}")
        return False

    try:
        # 启动实例
        subprocess.Popen([sys.executable, str(launcher_script)],
                        cwd=str(instance_dir))
        return True
    except Exception as e:
        print(f"启动失败: {{e}}")
        return False


def main():
    """主函数"""
    print("🌟 浏览器实例启动器")
    print("=" * 50)
    
    instances = get_available_instances()
    
    if not instances:
        print("没有找到可用的实例")
        print("请先使用 browser_manager.py 创建实例")
        input("按回车键退出...")
        return

    print("可用实例:")
    for i, instance in enumerate(instances, 1):
        print(f"  {{i}}. {{instance['display_name']}}")
        if instance['description']:
            print(f"     {{instance['description']}}")
        print(f"     图标: {{instance['icon']}}")
        print()
    
    # 用户选择
    while True:
        try:
            choice = input("请选择要启动的实例 (输入数字，0退出): ").strip()
            
            if choice == "0":
                print("再见！")
                break

            choice_num = int(choice)
            if 1 <= choice_num <= len(instances):
                selected_instance = instances[choice_num - 1]
                print(f"启动 {{selected_instance['display_name']}}...")

                if launch_instance(selected_instance['name']):
                    print(f"{{selected_instance['display_name']}} 启动成功")
                    break
                else:
                    print("启动失败")
            else:
                print("无效选择，请重新输入")

        except ValueError:
            print("请输入有效数字")
        except KeyboardInterrupt:
            print("\\n再见！")
            break


if __name__ == "__main__":
    main()
'''
        
        # 保存全局启动器
        global_launcher_path = self.scripts_dir / "launch_browser.py"
        with open(global_launcher_path, 'w', encoding='utf-8') as f:
            f.write(launcher_content)
        
        print(f"已创建全局启动器: {global_launcher_path}")
        
        # 创建Windows批处理版本
        if os.name == 'nt':
            batch_content = f'''@echo off
REM 浏览器实例全局启动器
REM 生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

title 浏览器实例启动器
cd /d "%~dp0"
python launch_browser.py
pause
'''
            batch_path = self.scripts_dir / "launch_browser.bat"
            with open(batch_path, 'w', encoding='gbk') as f:
                f.write(batch_content)

            print(f"已创建批处理启动器: {batch_path}")
        
        return True


def main():
    """命令行接口"""
    import argparse
    
    parser = argparse.ArgumentParser(description="启动脚本生成器")
    subparsers = parser.add_subparsers(dest="command", help="可用命令")
    
    # 生成单个实例启动脚本
    generate_parser = subparsers.add_parser("generate", help="生成实例启动脚本")
    generate_parser.add_argument("instance", help="实例名称")
    
    # 重新生成所有启动脚本
    regenerate_parser = subparsers.add_parser("regenerate", help="重新生成所有启动脚本")
    
    # 创建全局启动器
    global_parser = subparsers.add_parser("global", help="创建全局启动器")
    
    args = parser.parse_args()
    
    generator = LauncherGenerator()
    
    if args.command == "generate":
        generator.generate_all_launchers(args.instance)
    elif args.command == "regenerate":
        generator.regenerate_all_instances()
    elif args.command == "global":
        generator.create_global_launcher()
    else:
        parser.print_help()


if __name__ == "__main__":
    main()
