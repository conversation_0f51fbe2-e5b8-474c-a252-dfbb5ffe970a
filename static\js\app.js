// 现代化浏览器管理界面 - JavaScript

class BrowserManager {
    constructor() {
        this.currentPage = 1;
        this.pageSize = 20;
        this.totalCount = 0;
        this.instances = [];
        this.selectedInstances = new Set();
        this.currentEditingInstance = null;
        
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.loadInstances();
        this.loadGroups();
        this.loadIcons();
    }
    
    bindEvents() {
        // 创建浏览器按钮
        document.getElementById('createBrowserBtn').addEventListener('click', () => {
            this.showCreateModal();
        });
        
        // 批量操作
        document.getElementById('batchOperationBtn').addEventListener('click', (e) => {
            this.toggleDropdown('batchMenu');
        });

        // 批量操作菜单项
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-action="batch-start"]')) {
                this.batchStartInstances();
            } else if (e.target.matches('[data-action="batch-stop"]')) {
                this.batchStopInstances();
            } else if (e.target.matches('[data-action="batch-delete"]')) {
                this.batchDeleteInstances();
            }
        });
        
        // 同步按钮
        document.getElementById('syncBtn').addEventListener('click', () => {
            this.loadInstances();
        });
        
        // 搜索
        document.getElementById('searchInput').addEventListener('input', (e) => {
            this.filterInstances(e.target.value);
        });
        
        // 全选复选框
        document.getElementById('selectAll').addEventListener('change', (e) => {
            this.toggleSelectAll(e.target.checked);
        });
        
        // 模态框事件
        document.getElementById('modalClose').addEventListener('click', () => {
            this.hideModal('browserModal');
        });
        
        document.getElementById('modalCancel').addEventListener('click', () => {
            this.hideModal('browserModal');
        });
        
        document.getElementById('modalSave').addEventListener('click', () => {
            this.saveBrowser();
        });
        
        // 确认删除模态框
        document.getElementById('confirmModalClose').addEventListener('click', () => {
            this.hideModal('confirmModal');
        });
        
        document.getElementById('confirmCancel').addEventListener('click', () => {
            this.hideModal('confirmModal');
        });
        
        document.getElementById('confirmDelete').addEventListener('click', () => {
            this.confirmDeleteInstance();
        });
        
        // 分页事件
        document.getElementById('prevPage').addEventListener('click', () => {
            if (this.currentPage > 1) {
                this.currentPage--;
                this.renderTable();
            }
        });
        
        document.getElementById('nextPage').addEventListener('click', () => {
            const totalPages = Math.ceil(this.totalCount / this.pageSize);
            if (this.currentPage < totalPages) {
                this.currentPage++;
                this.renderTable();
            }
        });
        
        // 页面跳转
        document.getElementById('jumpPage').addEventListener('change', (e) => {
            const page = parseInt(e.target.value);
            const totalPages = Math.ceil(this.totalCount / this.pageSize);
            if (page >= 1 && page <= totalPages) {
                this.currentPage = page;
                this.renderTable();
            }
        });
        
        // 点击空白处关闭下拉菜单
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.dropdown')) {
                this.hideAllDropdowns();
            }
        });
    }
    
    async loadInstances() {
        this.showLoading();
        try {
            const response = await fetch('/api/instances');
            const result = await response.json();
            
            if (result.success) {
                this.instances = result.data;
                this.totalCount = result.total;
                this.renderTable();
                this.updatePagination();
            } else {
                this.showToast('加载实例失败: ' + result.error, 'error');
            }
        } catch (error) {
            this.showToast('网络错误: ' + error.message, 'error');
        } finally {
            this.hideLoading();
        }
    }
    
    async loadGroups() {
        try {
            const response = await fetch('/api/groups');
            const result = await response.json();
            
            if (result.success) {
                const groupSelect = document.getElementById('browserGroup');
                groupSelect.innerHTML = '';
                
                result.data.forEach(group => {
                    const option = document.createElement('option');
                    option.value = group;
                    option.textContent = group;
                    groupSelect.appendChild(option);
                });
            }
        } catch (error) {
            console.error('加载分组失败:', error);
        }
    }
    
    async loadIcons() {
        try {
            const response = await fetch('/api/icons');
            const result = await response.json();
            
            if (result.success) {
                const iconSelect = document.getElementById('browserIcon');
                iconSelect.innerHTML = '';
                
                result.data.forEach(icon => {
                    const option = document.createElement('option');
                    option.value = icon;
                    option.textContent = icon;
                    iconSelect.appendChild(option);
                });
            }
        } catch (error) {
            console.error('加载图标失败:', error);
        }
    }
    
    renderTable() {
        const tbody = document.getElementById('browsersTableBody');
        tbody.innerHTML = '';
        
        // 计算当前页的数据
        const startIndex = (this.currentPage - 1) * this.pageSize;
        const endIndex = startIndex + this.pageSize;
        const pageData = this.instances.slice(startIndex, endIndex);
        
        pageData.forEach((instance, index) => {
            const row = this.createTableRow(instance, startIndex + index + 1);
            tbody.appendChild(row);
        });
        
        // 更新统计信息
        document.getElementById('totalCount').textContent = this.totalCount;
    }
    
    createTableRow(instance, index) {
        const row = document.createElement('tr');
        
        row.innerHTML = `
            <td>
                <input type="checkbox" class="instance-checkbox" data-name="${instance.name}" onchange="browserManager.onInstanceCheckboxChange(this)">
            </td>
            <td>${index}</td>
            <td>${instance.display_name || instance.name}</td>
            <td><span class="group-tag">${instance.group}</span></td>
            <td>
                <div class="icon-display" style="background-image: url('/static/images/${instance.icon}.ico')" title="${instance.icon}"></div>
            </td>
            <td>${instance.description || '无描述'}</td>
            <td>${instance.last_launch}</td>
            <td>
                <span class="status-badge ${instance.status === 'running' ? 'status-running' : 'status-stopped'}">
                    ${instance.status === 'running' ? '运行中' : '已停止'}
                </span>
            </td>
            <td>
                <div class="action-buttons">
                    ${instance.status === 'running' 
                        ? `<button class="btn btn-secondary" onclick="browserManager.stopInstance('${instance.name}')">
                             <i class="fas fa-stop"></i> 停止
                           </button>`
                        : `<button class="btn btn-success" onclick="browserManager.launchInstance('${instance.name}')">
                             <i class="fas fa-play"></i> 启动
                           </button>`
                    }
                    <button class="btn btn-outline" onclick="browserManager.editInstance('${instance.name}')" title="编辑实例">
                        <i class="fas fa-edit"></i> 编辑
                    </button>
                    <button class="btn btn-outline" onclick="browserManager.copyInstance('${instance.name}')" title="复制实例">
                        <i class="fas fa-copy"></i> 复制
                    </button>
                    <button class="btn btn-danger" onclick="browserManager.deleteInstance('${instance.name}')" title="删除实例">
                        <i class="fas fa-trash"></i> 删除
                    </button>
                </div>
            </td>
        `;
        
        return row;
    }
    
    showCreateModal() {
        this.currentEditingInstance = null;
        document.getElementById('modalTitle').textContent = '创建浏览器';
        document.getElementById('browserForm').reset();
        
        // 生成默认名称
        this.generateDefaultName();
        
        this.showModal('browserModal');
    }
    
    generateDefaultName() {
        const existingNames = this.instances.map(inst => inst.name);

        // 查找已存在的"浏览器X"格式的名称
        const usedNumbers = new Set();
        existingNames.forEach(name => {
            if (name.startsWith('浏览器') && name.length > 3) {
                const suffix = name.substring(3); // 去掉"浏览器"前缀
                const num = parseInt(suffix);
                if (!isNaN(num)) {
                    usedNumbers.add(num);
                }
            }
        });

        // 找到第一个未使用的数字
        let defaultNumber = 1;
        while (usedNumbers.has(defaultNumber)) {
            defaultNumber++;
        }

        const defaultName = `浏览器${defaultNumber}`;
        const defaultDisplayName = `Chrome浏览器${defaultNumber}`;
        const defaultDescription = `Chrome浏览器${defaultNumber}`;

        // 设置表单值
        document.getElementById('browserName').value = defaultName;
        document.getElementById('displayName').value = defaultDisplayName;
        document.getElementById('browserDescription').value = defaultDescription;
    }
    
    async editInstance(instanceName) {
        const instance = this.instances.find(inst => inst.name === instanceName);
        if (!instance) return;
        
        this.currentEditingInstance = instanceName;
        document.getElementById('modalTitle').textContent = '编辑浏览器';
        
        // 填充表单
        document.getElementById('browserName').value = instance.name;
        document.getElementById('displayName').value = instance.display_name || '';
        document.getElementById('browserGroup').value = instance.group || '默认分组';
        document.getElementById('browserIcon').value = instance.icon || 'default';
        document.getElementById('browserDescription').value = instance.description || '';
        
        this.showModal('browserModal');
    }

    async copyInstance(instanceName) {
        const instance = this.instances.find(inst => inst.name === instanceName);
        if (!instance) return;

        // 生成新的实例名称
        const baseName = instance.name;
        let newName = `${baseName}_副本`;
        let counter = 1;

        const existingNames = this.instances.map(inst => inst.name);
        while (existingNames.includes(newName)) {
            counter++;
            newName = `${baseName}_副本${counter}`;
        }

        // 创建复制的实例数据
        const copyData = {
            name: baseName,
            display_name: `${instance.display_name || instance.name} - 副本`,
            group: instance.group,
            icon: instance.icon,
            description: `复制自: ${instance.description || instance.name}`
        };

        this.showLoading();

        try {
            const response = await fetch(`/api/instances/${instanceName}/copy`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(copyData)
            });

            const result = await response.json();

            if (result.success) {
                const newInstanceName = result.data?.name || newName;
                this.showToast(`实例 "${newInstanceName}" 复制成功`, 'success');
                this.loadInstances();
            } else {
                this.showToast(result.error, 'error');
            }
        } catch (error) {
            this.showToast('网络错误: ' + error.message, 'error');
        } finally {
            this.hideLoading();
        }
    }

    async saveBrowser() {
        const form = document.getElementById('browserForm');
        const formData = new FormData(form);
        
        const data = {
            name: formData.get('name').trim(),
            display_name: formData.get('display_name').trim(),
            group: formData.get('group'),
            icon: formData.get('icon'),
            description: formData.get('description').trim()
        };

        // 对于编辑操作，名称不能为空
        if (this.currentEditingInstance && !data.name) {
            this.showToast('请输入实例名称', 'warning');
            return;
        }
        
        this.showLoading();
        
        try {
            let response;
            if (this.currentEditingInstance) {
                // 更新实例
                response = await fetch(`/api/instances/${this.currentEditingInstance}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });
            } else {
                // 创建实例
                response = await fetch('/api/instances', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });
            }
            
            const result = await response.json();
            
            if (result.success) {
                this.showToast(result.message, 'success');
                this.hideModal('browserModal');
                this.loadInstances();
            } else {
                this.showToast(result.error, 'error');
            }
        } catch (error) {
            this.showToast('网络错误: ' + error.message, 'error');
        } finally {
            this.hideLoading();
        }
    }
    
    deleteInstance(instanceName) {
        this.currentEditingInstance = instanceName;
        document.getElementById('confirmMessage').textContent = `确定要删除浏览器实例 "${instanceName}" 吗？`;
        this.showModal('confirmModal');
    }
    
    async confirmDeleteInstance() {
        if (!this.currentEditingInstance) return;
        
        this.showLoading();
        
        try {
            const response = await fetch(`/api/instances/${this.currentEditingInstance}`, {
                method: 'DELETE'
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showToast(result.message, 'success');
                this.hideModal('confirmModal');
                this.loadInstances();
            } else {
                this.showToast(result.error, 'error');
            }
        } catch (error) {
            this.showToast('网络错误: ' + error.message, 'error');
        } finally {
            this.hideLoading();
        }
    }
    
    async launchInstance(instanceName) {
        this.showLoading();
        
        try {
            const response = await fetch(`/api/instances/${instanceName}/launch`, {
                method: 'POST'
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showToast(result.message, 'success');
                this.loadInstances();
            } else {
                this.showToast(result.error, 'error');
            }
        } catch (error) {
            this.showToast('网络错误: ' + error.message, 'error');
        } finally {
            this.hideLoading();
        }
    }
    
    async stopInstance(instanceName) {
        this.showLoading();
        
        try {
            const response = await fetch(`/api/instances/${instanceName}/stop`, {
                method: 'POST'
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showToast(result.message, 'success');
                this.loadInstances();
            } else {
                this.showToast(result.error, 'error');
            }
        } catch (error) {
            this.showToast('网络错误: ' + error.message, 'error');
        } finally {
            this.hideLoading();
        }
    }
    
    filterInstances(searchTerm) {
        // 简单的前端过滤，实际项目中应该在后端实现
        const filtered = this.instances.filter(instance => 
            instance.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            (instance.display_name && instance.display_name.toLowerCase().includes(searchTerm.toLowerCase())) ||
            (instance.description && instance.description.toLowerCase().includes(searchTerm.toLowerCase()))
        );
        
        // 重新渲染表格
        const tbody = document.getElementById('browsersTableBody');
        tbody.innerHTML = '';
        
        filtered.forEach((instance, index) => {
            const row = this.createTableRow(instance, index + 1);
            tbody.appendChild(row);
        });
    }
    
    toggleSelectAll(checked) {
        const checkboxes = document.querySelectorAll('.instance-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = checked;
            if (checked) {
                this.selectedInstances.add(checkbox.dataset.name);
            } else {
                this.selectedInstances.delete(checkbox.dataset.name);
            }
        });
        this.updateBatchButtonState();
    }

    onInstanceCheckboxChange(checkbox) {
        const instanceName = checkbox.dataset.name;
        if (checkbox.checked) {
            this.selectedInstances.add(instanceName);
        } else {
            this.selectedInstances.delete(instanceName);
        }
        this.updateBatchButtonState();
    }

    updateBatchButtonState() {
        const batchBtn = document.getElementById('batchOperationBtn');
        const selectedCount = this.selectedInstances.size;

        if (selectedCount > 0) {
            batchBtn.textContent = `批量操作 (${selectedCount})`;
            batchBtn.disabled = false;
        } else {
            batchBtn.innerHTML = '<i class="fas fa-tasks"></i> 批量操作';
            batchBtn.disabled = false;
        }
    }

    async batchStartInstances() {
        if (this.selectedInstances.size === 0) {
            this.showToast('请先选择要启动的实例', 'warning');
            return;
        }

        this.showLoading();
        let successCount = 0;
        let failCount = 0;

        for (const instanceName of this.selectedInstances) {
            try {
                const response = await fetch(`/api/instances/${instanceName}/launch`, {
                    method: 'POST'
                });
                const result = await response.json();

                if (result.success) {
                    successCount++;
                } else {
                    failCount++;
                }
            } catch (error) {
                failCount++;
            }
        }

        this.hideLoading();
        this.showToast(`批量启动完成: 成功 ${successCount} 个，失败 ${failCount} 个`,
                      failCount === 0 ? 'success' : 'warning');
        this.loadInstances();
        this.selectedInstances.clear();
        this.updateBatchButtonState();
    }

    async batchStopInstances() {
        if (this.selectedInstances.size === 0) {
            this.showToast('请先选择要停止的实例', 'warning');
            return;
        }

        this.showLoading();
        let successCount = 0;
        let failCount = 0;

        for (const instanceName of this.selectedInstances) {
            try {
                const response = await fetch(`/api/instances/${instanceName}/stop`, {
                    method: 'POST'
                });
                const result = await response.json();

                if (result.success) {
                    successCount++;
                } else {
                    failCount++;
                }
            } catch (error) {
                failCount++;
            }
        }

        this.hideLoading();
        this.showToast(`批量停止完成: 成功 ${successCount} 个，失败 ${failCount} 个`,
                      failCount === 0 ? 'success' : 'warning');
        this.loadInstances();
        this.selectedInstances.clear();
        this.updateBatchButtonState();
    }

    async batchDeleteInstances() {
        if (this.selectedInstances.size === 0) {
            this.showToast('请先选择要删除的实例', 'warning');
            return;
        }

        const instanceNames = Array.from(this.selectedInstances);
        const confirmMessage = `确定要删除以下 ${instanceNames.length} 个实例吗？\n\n${instanceNames.join('\n')}`;

        if (!confirm(confirmMessage)) {
            return;
        }

        this.showLoading();
        let successCount = 0;
        let failCount = 0;

        for (const instanceName of this.selectedInstances) {
            try {
                const response = await fetch(`/api/instances/${instanceName}`, {
                    method: 'DELETE'
                });
                const result = await response.json();

                if (result.success) {
                    successCount++;
                } else {
                    failCount++;
                }
            } catch (error) {
                failCount++;
            }
        }

        this.hideLoading();
        this.showToast(`批量删除完成: 成功 ${successCount} 个，失败 ${failCount} 个`,
                      failCount === 0 ? 'success' : 'warning');
        this.loadInstances();
        this.selectedInstances.clear();
        this.updateBatchButtonState();
    }
    
    updatePagination() {
        const totalPages = Math.ceil(this.totalCount / this.pageSize);
        
        // 更新上一页/下一页按钮状态
        document.getElementById('prevPage').disabled = this.currentPage <= 1;
        document.getElementById('nextPage').disabled = this.currentPage >= totalPages;
        
        // 更新页码显示
        const pageNumbers = document.getElementById('pageNumbers');
        pageNumbers.innerHTML = '';
        
        for (let i = 1; i <= totalPages; i++) {
            const pageBtn = document.createElement('button');
            pageBtn.className = `page-btn ${i === this.currentPage ? 'active' : ''}`;
            pageBtn.textContent = i;
            pageBtn.addEventListener('click', () => {
                this.currentPage = i;
                this.renderTable();
                this.updatePagination();
            });
            pageNumbers.appendChild(pageBtn);
        }
        
        // 更新跳转页码
        document.getElementById('jumpPage').value = this.currentPage;
    }
    
    showModal(modalId) {
        document.getElementById(modalId).classList.add('show');
    }
    
    hideModal(modalId) {
        document.getElementById(modalId).classList.remove('show');
    }
    
    toggleDropdown(menuId) {
        const menu = document.getElementById(menuId);
        menu.classList.toggle('show');
    }
    
    hideAllDropdowns() {
        document.querySelectorAll('.dropdown-menu').forEach(menu => {
            menu.classList.remove('show');
        });
    }
    
    showLoading() {
        document.getElementById('loading').classList.add('show');
    }
    
    hideLoading() {
        document.getElementById('loading').classList.remove('show');
    }
    
    showToast(message, type = 'info') {
        const toast = document.getElementById('toast');
        const icon = toast.querySelector('.toast-icon');
        const messageEl = toast.querySelector('.toast-message');
        
        // 设置图标
        const icons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };
        
        icon.className = `toast-icon ${icons[type] || icons.info}`;
        messageEl.textContent = message;
        
        // 设置样式
        toast.className = `toast ${type} show`;
        
        // 3秒后自动隐藏
        setTimeout(() => {
            toast.classList.remove('show');
        }, 3000);
    }
}

// 初始化应用
const browserManager = new BrowserManager();
