#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Chrome浏览器1 启动脚本
自动生成，请勿手动修改
"""

import os
import subprocess
from pathlib import Path

def main():
    # 获取脚本所在目录
    script_dir = Path(__file__).parent
    base_dir = script_dir.parent.parent

    # Chrome Portable路径
    chrome_exe = base_dir / "GoogleChromePortable" / "App" / "Chrome-bin" / "chrome.exe"

    # 用户数据目录
    user_data_dir = script_dir / "Data" / "profile"

    # 构建启动命令
    cmd = [
        str(chrome_exe),
        f"--user-data-dir={user_data_dir}",
        "--no-first-run",
        "--no-default-browser-check",
        "--disable-default-apps",
        "--disable-background-mode",
    ]

    # 启动Chrome
    try:
        subprocess.Popen(cmd)
        print(f"已启动 Chrome浏览器1")
    except Exception as e:
        print(f"启动失败: {e}")

if __name__ == "__main__":
    main()
