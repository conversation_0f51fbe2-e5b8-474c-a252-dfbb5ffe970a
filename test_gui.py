#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI界面测试脚本
测试GUI界面的基本功能和组件
"""

import sys
import os
import time
import threading
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def test_imports():
    """测试模块导入"""
    print("测试模块导入...")
    
    try:
        import tkinter as tk
        print("✓ tkinter 导入成功")
    except ImportError as e:
        print(f"✗ tkinter 导入失败: {e}")
        return False
    
    try:
        from PIL import Image, ImageTk
        print("✓ PIL 导入成功")
    except ImportError as e:
        print(f"✗ PIL 导入失败: {e}")
        return False
    
    try:
        import psutil
        print("✓ psutil 导入成功")
    except ImportError as e:
        print(f"✗ psutil 导入失败: {e}")
        return False
    
    try:
        from gui_launcher import BrowserLauncherGUI, ProcessMonitor, InstanceCard
        print("✓ GUI模块导入成功")
    except ImportError as e:
        print(f"✗ GUI模块导入失败: {e}")
        return False
    
    try:
        from gui_dialogs import NewInstanceDialog, BatchConfigDialog
        print("✓ 对话框模块导入成功")
    except ImportError as e:
        print(f"✗ 对话框模块导入失败: {e}")
        return False
    
    return True

def test_process_monitor():
    """测试进程监控器"""
    print("\n测试进程监控器...")
    
    try:
        from gui_launcher import ProcessMonitor
        
        monitor = ProcessMonitor()
        
        # 测试检查实例运行状态
        is_running = monitor.is_instance_running("test_instance")
        print(f"✓ 实例状态检查: {is_running}")
        
        # 测试获取运行中的实例
        running_instances = monitor.get_running_instances()
        print(f"✓ 运行中实例: {len(running_instances)} 个")
        
        return True
        
    except Exception as e:
        print(f"✗ 进程监控器测试失败: {e}")
        return False

def test_gui_creation():
    """测试GUI界面创建"""
    print("\n测试GUI界面创建...")
    
    try:
        import tkinter as tk
        from gui_launcher import BrowserLauncherGUI
        
        # 创建测试窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        # 测试GUI组件创建
        app = BrowserLauncherGUI()
        app.root.withdraw()  # 隐藏主窗口
        
        print("✓ GUI界面创建成功")
        
        # 测试界面组件
        if hasattr(app, 'browser_manager'):
            print("✓ 浏览器管理器初始化成功")
        
        if hasattr(app, 'icon_manager'):
            print("✓ 图标管理器初始化成功")
        
        if hasattr(app, 'process_monitor'):
            print("✓ 进程监控器初始化成功")
        
        # 清理
        app.root.destroy()
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"✗ GUI界面创建失败: {e}")
        return False

def test_dialog_creation():
    """测试对话框创建"""
    print("\n测试对话框创建...")
    
    try:
        import tkinter as tk
        from gui_dialogs import NewInstanceDialog, BatchConfigDialog
        
        # 创建测试根窗口
        root = tk.Tk()
        root.withdraw()
        
        # 测试新建实例对话框
        try:
            dialog = NewInstanceDialog(root)
            dialog.dialog.withdraw()  # 隐藏对话框
            dialog.dialog.destroy()
            print("✓ 新建实例对话框创建成功")
        except Exception as e:
            print(f"✗ 新建实例对话框创建失败: {e}")
        
        # 测试批量配置对话框
        try:
            dialog = BatchConfigDialog(root)
            dialog.dialog.withdraw()  # 隐藏对话框
            dialog.dialog.destroy()
            print("✓ 批量配置对话框创建成功")
        except Exception as e:
            print(f"✗ 批量配置对话框创建失败: {e}")
        
        # 清理
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"✗ 对话框创建失败: {e}")
        return False

def test_file_structure():
    """测试文件结构"""
    print("\n测试文件结构...")
    
    required_files = [
        "gui_launcher.py",
        "gui_dialogs.py",
        "启动GUI界面.py",
        "启动GUI界面.bat"
    ]
    
    required_dirs = [
        "tools",
        "instances",
        "icons",
        "scripts"
    ]
    
    all_good = True
    
    # 检查文件
    for file_name in required_files:
        file_path = Path(file_name)
        if file_path.exists():
            print(f"✓ 文件存在: {file_name}")
        else:
            print(f"✗ 文件缺失: {file_name}")
            all_good = False
    
    # 检查目录
    for dir_name in required_dirs:
        dir_path = Path(dir_name)
        if dir_path.exists() and dir_path.is_dir():
            print(f"✓ 目录存在: {dir_name}")
        else:
            print(f"✗ 目录缺失: {dir_name}")
            all_good = False
    
    return all_good

def main():
    """主测试函数"""
    print("=" * 50)
    print("GUI界面功能测试")
    print("=" * 50)
    
    tests = [
        ("文件结构检查", test_file_structure),
        ("模块导入测试", test_imports),
        ("进程监控器测试", test_process_monitor),
        ("GUI界面创建测试", test_gui_creation),
        ("对话框创建测试", test_dialog_creation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 通过")
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    print("=" * 50)
    
    if passed == total:
        print("🎉 所有测试通过！GUI界面可以正常使用。")
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关问题。")
        return False

if __name__ == "__main__":
    success = main()
    
    if not success:
        print("\n按回车键退出...")
        input()
    else:
        print("\n测试完成！")
