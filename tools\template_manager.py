#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置模板管理工具
管理和创建各种配置文件模板
"""

import os
import json
from pathlib import Path
from datetime import datetime


class TemplateManager:
    """配置模板管理器"""
    
    def __init__(self, base_dir=None):
        """初始化模板管理器"""
        if base_dir is None:
            self.base_dir = Path(__file__).parent.parent
        else:
            self.base_dir = Path(base_dir)
            
        self.templates_dir = self.base_dir / "tools" / "config_templates"
        
        # 确保模板目录存在
        self.templates_dir.mkdir(exist_ok=True)
    
    def create_instance_template(self):
        """创建单实例配置模板"""
        template = {
            "name": "example_instance",
            "display_name": "Chrome - 示例",
            "description": "示例浏览器实例",
            "icon": "default.ico",
            "chrome_args": [
                "--no-first-run",
                "--no-default-browser-check",
                "--disable-background-timer-throttling",
                "--disable-backgrounding-occluded-windows",
                "--disable-renderer-backgrounding",
                "--disable-features=TranslateUI",
                "--disable-ipc-flooding-protection"
            ],
            "window_settings": {
                "width": 1200,
                "height": 800,
                "position": "center"
            },
            "startup_options": {
                "homepage": "https://www.google.com",
                "restore_session": False,
                "incognito": False
            },
            "advanced_settings": {
                "user_agent": "",
                "proxy": "",
                "extensions_disabled": False,
                "javascript_disabled": False
            },
            "created_date": datetime.now().isoformat(),
            "last_modified": datetime.now().isoformat()
        }
        
        template_path = self.templates_dir / "instance_template.json"
        with open(template_path, 'w', encoding='utf-8') as f:
            json.dump(template, f, ensure_ascii=False, indent=2)
        
        print(f"已创建实例配置模板: {template_path}")
        return template_path
    
    def create_batch_templates(self):
        """创建批量配置模板"""
        templates = {
            "minimal_set.json": {
                "name": "最小实例集合",
                "description": "包含工作和个人两个基础实例",
                "instances": [
                    {
                        "name": "work",
                        "display_name": "Chrome - 工作",
                        "description": "工作专用浏览器",
                        "homepage": "https://www.google.com",
                        "icon": "work"
                    },
                    {
                        "name": "personal",
                        "display_name": "Chrome - 个人",
                        "description": "个人使用浏览器",
                        "homepage": "https://www.baidu.com",
                        "icon": "personal"
                    }
                ]
            },
            "developer_set.json": {
                "name": "开发者实例集合",
                "description": "专为开发者设计的实例集合",
                "instances": [
                    {
                        "name": "dev_main",
                        "display_name": "Chrome - 开发主环境",
                        "description": "主要开发环境",
                        "homepage": "https://github.com",
                        "icon": "dev"
                    },
                    {
                        "name": "dev_test",
                        "display_name": "Chrome - 测试环境",
                        "description": "测试环境浏览器",
                        "homepage": "http://localhost:3000",
                        "icon": "dev"
                    },
                    {
                        "name": "dev_docs",
                        "display_name": "Chrome - 文档查阅",
                        "description": "查阅技术文档专用",
                        "homepage": "https://developer.mozilla.org",
                        "icon": "education"
                    }
                ]
            },
            "business_set.json": {
                "name": "商务实例集合",
                "description": "适合商务人士的实例配置",
                "instances": [
                    {
                        "name": "work_main",
                        "display_name": "Chrome - 主要工作",
                        "description": "主要工作账号",
                        "homepage": "https://www.office.com",
                        "icon": "work"
                    },
                    {
                        "name": "work_backup",
                        "display_name": "Chrome - 备用工作",
                        "description": "备用工作账号",
                        "homepage": "https://www.google.com",
                        "icon": "work"
                    },
                    {
                        "name": "finance",
                        "display_name": "Chrome - 财务",
                        "description": "财务管理专用",
                        "homepage": "https://www.alipay.com",
                        "icon": "finance"
                    },
                    {
                        "name": "communication",
                        "display_name": "Chrome - 沟通",
                        "description": "邮件和沟通工具",
                        "homepage": "https://mail.qq.com",
                        "icon": "social"
                    }
                ]
            },
            "student_set.json": {
                "name": "学生实例集合",
                "description": "适合学生使用的实例配置",
                "instances": [
                    {
                        "name": "study",
                        "display_name": "Chrome - 学习",
                        "description": "学习专用浏览器",
                        "homepage": "https://www.coursera.org",
                        "icon": "education"
                    },
                    {
                        "name": "research",
                        "display_name": "Chrome - 研究",
                        "description": "学术研究专用",
                        "homepage": "https://scholar.google.com",
                        "icon": "education"
                    },
                    {
                        "name": "entertainment",
                        "display_name": "Chrome - 娱乐",
                        "description": "娱乐休闲专用",
                        "homepage": "https://www.bilibili.com",
                        "icon": "entertainment"
                    }
                ]
            },
            "ecommerce_set.json": {
                "name": "电商实例集合",
                "description": "专为电商从业者设计",
                "instances": [
                    {
                        "name": "shop_taobao",
                        "display_name": "Chrome - 淘宝",
                        "description": "淘宝店铺管理",
                        "homepage": "https://www.taobao.com",
                        "icon": "shopping"
                    },
                    {
                        "name": "shop_tmall",
                        "display_name": "Chrome - 天猫",
                        "description": "天猫店铺管理",
                        "homepage": "https://www.tmall.com",
                        "icon": "shopping"
                    },
                    {
                        "name": "shop_jd",
                        "display_name": "Chrome - 京东",
                        "description": "京东店铺管理",
                        "homepage": "https://www.jd.com",
                        "icon": "shopping"
                    },
                    {
                        "name": "shop_analysis",
                        "display_name": "Chrome - 数据分析",
                        "description": "电商数据分析",
                        "homepage": "https://www.google.com/analytics",
                        "icon": "finance"
                    }
                ]
            }
        }
        
        created_count = 0
        for filename, template in templates.items():
            template_path = self.templates_dir / filename
            try:
                with open(template_path, 'w', encoding='utf-8') as f:
                    json.dump(template, f, ensure_ascii=False, indent=2)
                print(f"已创建批量配置模板: {template_path}")
                created_count += 1
            except Exception as e:
                print(f"创建模板失败 {filename}: {e}")
        
        return created_count
    
    def create_chrome_args_templates(self):
        """创建Chrome参数配置模板"""
        templates = {
            "performance_optimized.json": {
                "name": "性能优化配置",
                "description": "优化Chrome性能的参数配置",
                "chrome_args": [
                    "--no-first-run",
                    "--no-default-browser-check",
                    "--disable-background-timer-throttling",
                    "--disable-backgrounding-occluded-windows",
                    "--disable-renderer-backgrounding",
                    "--disable-features=TranslateUI",
                    "--disable-ipc-flooding-protection",
                    "--max_old_space_size=4096",
                    "--disable-extensions-except",
                    "--disable-plugins-discovery",
                    "--disable-preconnect"
                ]
            },
            "privacy_focused.json": {
                "name": "隐私保护配置",
                "description": "注重隐私保护的参数配置",
                "chrome_args": [
                    "--no-first-run",
                    "--no-default-browser-check",
                    "--incognito",
                    "--disable-background-timer-throttling",
                    "--disable-backgrounding-occluded-windows",
                    "--disable-renderer-backgrounding",
                    "--disable-features=TranslateUI",
                    "--disable-sync",
                    "--disable-background-networking",
                    "--disable-default-apps",
                    "--disable-extensions",
                    "--no-pings",
                    "--no-referrers"
                ]
            },
            "development_friendly.json": {
                "name": "开发友好配置",
                "description": "适合开发调试的参数配置",
                "chrome_args": [
                    "--no-first-run",
                    "--no-default-browser-check",
                    "--disable-background-timer-throttling",
                    "--disable-backgrounding-occluded-windows",
                    "--disable-renderer-backgrounding",
                    "--disable-features=TranslateUI",
                    "--disable-web-security",
                    "--disable-features=VizDisplayCompositor",
                    "--allow-running-insecure-content",
                    "--disable-extensions-except",
                    "--load-extension"
                ]
            }
        }
        
        args_dir = self.templates_dir / "chrome_args"
        args_dir.mkdir(exist_ok=True)
        
        created_count = 0
        for filename, template in templates.items():
            template_path = args_dir / filename
            try:
                with open(template_path, 'w', encoding='utf-8') as f:
                    json.dump(template, f, ensure_ascii=False, indent=2)
                print(f"已创建Chrome参数模板: {template_path}")
                created_count += 1
            except Exception as e:
                print(f"创建参数模板失败 {filename}: {e}")
        
        return created_count
    
    def create_all_templates(self):
        """创建所有模板"""
        print("🎨 创建配置模板...")
        
        # 创建实例模板
        self.create_instance_template()
        
        # 创建批量配置模板
        batch_count = self.create_batch_templates()
        
        # 创建Chrome参数模板
        args_count = self.create_chrome_args_templates()
        
        print(f"\n模板创建完成:")
        print(f"  - 实例模板: 1 个")
        print(f"  - 批量配置模板: {batch_count} 个")
        print(f"  - Chrome参数模板: {args_count} 个")
        
        return True
    
    def list_all_templates(self):
        """列出所有模板"""
        templates = {
            "实例模板": [],
            "批量配置模板": [],
            "Chrome参数模板": []
        }
        
        # 扫描实例模板
        for template_file in self.templates_dir.glob("*template*.json"):
            templates["实例模板"].append(template_file.name)
        
        # 扫描批量配置模板
        for template_file in self.templates_dir.glob("*set*.json"):
            templates["批量配置模板"].append(template_file.name)
        
        # 扫描Chrome参数模板
        args_dir = self.templates_dir / "chrome_args"
        if args_dir.exists():
            for template_file in args_dir.glob("*.json"):
                templates["Chrome参数模板"].append(template_file.name)
        
        print("可用模板:")
        for category, files in templates.items():
            if files:
                print(f"\n{category}:")
                for file in files:
                    print(f"  - {file}")
            else:
                print(f"\n{category}: 无")
        
        return templates


def main():
    """命令行接口"""
    import argparse
    
    parser = argparse.ArgumentParser(description="配置模板管理工具")
    subparsers = parser.add_subparsers(dest="command", help="可用命令")
    
    # 创建所有模板
    create_all_parser = subparsers.add_parser("create-all", help="创建所有模板")
    
    # 创建实例模板
    create_instance_parser = subparsers.add_parser("create-instance", help="创建实例模板")
    
    # 创建批量模板
    create_batch_parser = subparsers.add_parser("create-batch", help="创建批量配置模板")
    
    # 创建参数模板
    create_args_parser = subparsers.add_parser("create-args", help="创建Chrome参数模板")
    
    # 列出模板
    list_parser = subparsers.add_parser("list", help="列出所有模板")
    
    args = parser.parse_args()
    
    manager = TemplateManager()
    
    if args.command == "create-all":
        manager.create_all_templates()
    elif args.command == "create-instance":
        manager.create_instance_template()
    elif args.command == "create-batch":
        manager.create_batch_templates()
    elif args.command == "create-args":
        manager.create_chrome_args_templates()
    elif args.command == "list":
        manager.list_all_templates()
    else:
        parser.print_help()


if __name__ == "__main__":
    main()
