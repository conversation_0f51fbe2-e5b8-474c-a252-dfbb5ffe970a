#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试编辑和复制实例功能
"""

import sys
import os
import json
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))
sys.path.append(str(Path(__file__).parent / "tools"))

def test_browser_manager_methods():
    """测试BrowserManager的新方法"""
    print("🔍 测试BrowserManager新方法...")
    
    try:
        from browser_manager import BrowserManager
        
        manager = BrowserManager()
        
        # 测试list_instances
        instances = manager.list_instances()
        print(f"✅ 获取到 {len(instances)} 个实例")
        
        if not instances:
            print("❌ 没有实例可供测试")
            return False
        
        # 选择第一个实例进行测试
        test_instance = instances[0]
        instance_name = test_instance['name']
        print(f"📋 使用实例 '{instance_name}' 进行测试")
        
        # 测试update_instance方法
        print("\n🔧 测试update_instance方法...")
        original_config = test_instance['config'].copy()
        
        # 修改配置
        test_config = original_config.copy()
        test_config['description'] = "测试修改的描述"
        test_config['display_name'] = f"{original_config.get('display_name', instance_name)} - 已测试"
        
        # 更新实例
        success = manager.update_instance(instance_name, test_config)
        if success:
            print("✅ update_instance 方法正常")
            
            # 恢复原始配置
            manager.update_instance(instance_name, original_config)
            print("✅ 配置已恢复")
        else:
            print("❌ update_instance 方法失败")
            return False
        
        # 测试copy_instance方法
        print("\n📋 测试copy_instance方法...")
        copy_name = f"{instance_name}_test_copy"
        
        # 检查复制目标是否已存在
        existing_names = [inst['name'] for inst in manager.list_instances()]
        if copy_name in existing_names:
            print(f"⚠️ 测试复制目标 '{copy_name}' 已存在，先删除...")
            manager.delete_instance(copy_name)
        
        # 执行复制
        copy_config = {
            'instance_name': copy_name,
            'display_name': f"{original_config.get('display_name', instance_name)} - 测试副本",
            'description': f"测试复制自 {instance_name}",
            'icon': original_config.get('icon', 'default')
        }
        
        success = manager.copy_instance(instance_name, copy_name, copy_config)
        if success:
            print("✅ copy_instance 方法正常")
            
            # 验证复制结果
            new_instances = manager.list_instances()
            copy_found = any(inst['name'] == copy_name for inst in new_instances)
            
            if copy_found:
                print("✅ 复制的实例已创建")
                
                # 清理测试数据
                manager.delete_instance(copy_name)
                print("✅ 测试数据已清理")
            else:
                print("❌ 复制的实例未找到")
                return False
        else:
            print("❌ copy_instance 方法失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ BrowserManager方法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_dialog_imports():
    """测试对话框导入"""
    print("\n🔍 测试对话框导入...")
    
    try:
        from gui_dialogs import EditInstanceDialog, CopyInstanceDialog
        print("✅ EditInstanceDialog 导入成功")
        print("✅ CopyInstanceDialog 导入成功")
        return True
        
    except Exception as e:
        print(f"❌ 对话框导入失败: {e}")
        return False

def test_dialog_creation():
    """测试对话框创建"""
    print("\n🔍 测试对话框创建...")
    
    try:
        import tkinter as tk
        from gui_dialogs import EditInstanceDialog, CopyInstanceDialog
        from browser_manager import BrowserManager
        
        # 获取测试实例
        manager = BrowserManager()
        instances = manager.list_instances()
        
        if not instances:
            print("❌ 没有实例可供测试")
            return False
        
        test_instance_name = instances[0]['name']
        
        # 创建临时根窗口
        root = tk.Tk()
        root.withdraw()
        
        # 测试EditInstanceDialog创建
        print(f"📝 测试EditInstanceDialog创建 (实例: {test_instance_name})...")
        try:
            edit_dialog = EditInstanceDialog(root, test_instance_name)
            if hasattr(edit_dialog, 'dialog'):
                print("✅ EditInstanceDialog 创建成功")
                edit_dialog.dialog.destroy()
            else:
                print("❌ EditInstanceDialog 创建失败")
                return False
        except Exception as e:
            print(f"❌ EditInstanceDialog 创建异常: {e}")
            return False
        
        # 测试CopyInstanceDialog创建
        print(f"📋 测试CopyInstanceDialog创建 (源实例: {test_instance_name})...")
        try:
            copy_dialog = CopyInstanceDialog(root, test_instance_name)
            if hasattr(copy_dialog, 'dialog'):
                print("✅ CopyInstanceDialog 创建成功")
                copy_dialog.dialog.destroy()
            else:
                print("❌ CopyInstanceDialog 创建失败")
                return False
        except Exception as e:
            print(f"❌ CopyInstanceDialog 创建异常: {e}")
            return False
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 对话框创建测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gui_integration():
    """测试GUI集成"""
    print("\n🔍 测试GUI集成...")
    
    try:
        import tkinter as tk
        from gui_launcher import BrowserLauncherGUI
        
        # 创建GUI实例
        root = tk.Tk()
        root.withdraw()
        
        app = BrowserLauncherGUI()
        app.root.withdraw()
        
        # 检查方法是否存在
        methods_to_check = ['edit_instance', 'copy_instance']
        
        for method_name in methods_to_check:
            if hasattr(app, method_name):
                print(f"✅ {method_name} 方法存在")
            else:
                print(f"❌ {method_name} 方法缺失")
                return False
        
        app.root.destroy()
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ GUI集成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("🧪 编辑和复制实例功能测试")
    print("=" * 60)
    
    tests = [
        ("BrowserManager新方法", test_browser_manager_methods),
        ("对话框导入", test_dialog_imports),
        ("对话框创建", test_dialog_creation),
        ("GUI集成", test_gui_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*15} {test_name} {'='*15}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 60)
    print("📊 测试结果")
    print("=" * 60)
    
    if passed == total:
        print("🎉 所有测试通过！编辑和复制功能已就绪！")
        print("\n✅ 功能特性:")
        print("• 编辑实例配置（显示名称、描述、图标、主页、窗口大小）")
        print("• 复制实例（保留配置，允许修改基本信息）")
        print("• 输入验证和错误处理")
        print("• 与GUI界面完整集成")
        
        print("\n🚀 使用方法:")
        print("1. 右键点击实例卡片")
        print("2. 选择'编辑实例'或'复制实例'")
        print("3. 在对话框中修改配置")
        print("4. 点击'保存'或'复制'完成操作")
        
        return True
    else:
        print(f"⚠️ {total - passed}/{total} 个测试失败")
        print("需要检查相关功能实现")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🎯 建议:")
            print("1. 启动GUI界面测试编辑功能")
            print("2. 测试复制功能")
            print("3. 验证配置保存是否正确")
    except Exception as e:
        print(f"\n测试异常: {e}")
    
    print("\n按回车键退出...")
    input()
