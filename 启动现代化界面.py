#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动现代化Web界面 - 简化版本
"""

import sys
import os
import json
import webbrowser
import threading
import time
from pathlib import Path
from http.server import HTTPServer, SimpleHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import subprocess

# 设置无缓冲输出
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', line_buffering=True)
sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8', line_buffering=True)

# 添加tools目录到Python路径
sys.path.append(str(Path(__file__).parent / "tools"))

try:
    from browser_manager import BrowserManager
    from icon_manager import IconManager
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    print("请确保tools目录下的模块文件存在")
    sys.exit(1)

class ModernBrowserHandler(SimpleHTTPRequestHandler):
    """现代化浏览器管理界面处理器"""
    
    def __init__(self, *args, **kwargs):
        self.browser_manager = BrowserManager()
        self.icon_manager = IconManager()
        super().__init__(*args, **kwargs)
    
    def do_GET(self):
        """处理GET请求"""
        parsed_path = urlparse(self.path)
        
        if parsed_path.path == '/':
            self.serve_index()
        elif parsed_path.path.startswith('/api/'):
            self.handle_api_get(parsed_path)
        elif parsed_path.path.startswith('/static/'):
            self.serve_static_file(parsed_path.path)
        else:
            self.send_error(404)
    
    def do_POST(self):
        """处理POST请求"""
        parsed_path = urlparse(self.path)

        if parsed_path.path.startswith('/api/'):
            self.handle_api_post(parsed_path)
        else:
            self.send_error(404)

    def do_PUT(self):
        """处理PUT请求"""
        parsed_path = urlparse(self.path)

        if parsed_path.path.startswith('/api/'):
            self.handle_api_put(parsed_path)
        else:
            self.send_error(404)

    def do_DELETE(self):
        """处理DELETE请求"""
        parsed_path = urlparse(self.path)

        if parsed_path.path.startswith('/api/'):
            self.handle_api_delete(parsed_path)
        else:
            self.send_error(404)
    
    def serve_index(self):
        """提供主页面"""
        try:
            with open('templates/index.html', 'r', encoding='utf-8') as f:
                content = f.read()
            
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.end_headers()
            self.wfile.write(content.encode('utf-8'))
        except FileNotFoundError:
            self.send_error(404, "模板文件未找到")
    
    def serve_static_file(self, path):
        """提供静态文件"""
        try:
            # 移除/static/前缀
            file_path = path[8:]  # 去掉'/static/'
            
            # 处理CSS文件
            if file_path.endswith('.css'):
                with open(f'static/css/{file_path.split("/")[-1]}', 'r', encoding='utf-8') as f:
                    content = f.read()
                self.send_response(200)
                self.send_header('Content-type', 'text/css; charset=utf-8')
                self.end_headers()
                self.wfile.write(content.encode('utf-8'))
            
            # 处理JS文件
            elif file_path.endswith('.js'):
                with open(f'static/js/{file_path.split("/")[-1]}', 'r', encoding='utf-8') as f:
                    content = f.read()
                self.send_response(200)
                self.send_header('Content-type', 'application/javascript; charset=utf-8')
                self.end_headers()
                self.wfile.write(content.encode('utf-8'))
            
            # 处理图标文件
            elif file_path.endswith('.ico'):
                icon_name = file_path.split("/")[-1]
                icon_path = Path('icons') / icon_name
                if icon_path.exists():
                    with open(icon_path, 'rb') as f:
                        content = f.read()
                    self.send_response(200)
                    self.send_header('Content-type', 'image/x-icon')
                    self.end_headers()
                    self.wfile.write(content)
                else:
                    self.send_error(404)
            else:
                self.send_error(404)
                
        except FileNotFoundError:
            self.send_error(404)
    
    def handle_api_get(self, parsed_path):
        """处理API GET请求"""
        path = parsed_path.path
        print(f"🔍 API GET请求: {path}")

        if path == '/api/instances':
            self.api_get_instances()
        elif path == '/api/icons':
            self.api_get_icons()
        elif path == '/api/groups':
            self.api_get_groups()
        elif path == '/api/test-chrome':
            print("🧪 执行Chrome测试API")
            self.api_test_chrome()
        else:
            print(f"❌ 未找到API路由: {path}")
            self.send_error(404)
    
    def handle_api_post(self, parsed_path):
        """处理API POST请求"""
        path = parsed_path.path
        
        # 读取POST数据
        content_length = int(self.headers.get('Content-Length', 0))
        post_data = self.rfile.read(content_length)
        
        try:
            data = json.loads(post_data.decode('utf-8')) if post_data else {}
        except json.JSONDecodeError:
            self.send_json_response({'success': False, 'error': '无效的JSON数据'}, 400)
            return
        
        if path == '/api/instances':
            self.api_create_instance(data)
        elif path.startswith('/api/instances/') and path.endswith('/launch'):
            import urllib.parse
            instance_name = urllib.parse.unquote(path.split('/')[-2])
            print(f"🎯 启动请求: 原始路径={path}, 解析出的实例名={instance_name}")
            sys.stdout.flush()  # 强制刷新输出
            self.api_launch_instance(instance_name)
        elif path.startswith('/api/instances/') and path.endswith('/stop'):
            instance_name = path.split('/')[-2]
            self.api_stop_instance(instance_name)
        elif path.startswith('/api/instances/') and path.endswith('/copy'):
            instance_name = path.split('/')[-2]
            self.api_copy_instance(instance_name, data)
        else:
            self.send_error(404)

    def handle_api_put(self, parsed_path):
        """处理API PUT请求"""
        path = parsed_path.path

        # 读取PUT数据
        content_length = int(self.headers.get('Content-Length', 0))
        put_data = self.rfile.read(content_length)

        try:
            data = json.loads(put_data.decode('utf-8')) if put_data else {}
        except json.JSONDecodeError:
            self.send_json_response({'success': False, 'error': '无效的JSON数据'}, 400)
            return

        if path.startswith('/api/instances/'):
            # 提取实例名称
            parts = path.split('/')
            if len(parts) >= 4:
                instance_name = parts[3]
                self.api_update_instance(instance_name, data)
            else:
                self.send_error(404)
        else:
            self.send_error(404)

    def handle_api_delete(self, parsed_path):
        """处理API DELETE请求"""
        path = parsed_path.path

        if path.startswith('/api/instances/'):
            # 提取实例名称
            parts = path.split('/')
            if len(parts) >= 4:
                instance_name = parts[3]
                self.api_delete_instance(instance_name)
            else:
                self.send_error(404)
        else:
            self.send_error(404)
    
    def api_get_instances(self):
        """获取实例列表API"""
        try:
            instances = self.browser_manager.list_instances()
            
            enhanced_instances = []
            for i, instance in enumerate(instances, 1):
                config = instance.get('config', {})
                enhanced_instance = {
                    'id': i,
                    'name': instance['name'],
                    'display_name': config.get('display_name', instance['name']),
                    'description': config.get('description', ''),
                    'icon': config.get('icon', 'default'),
                    'group': config.get('group', '默认分组'),
                    'last_launch': config.get('last_launch', '未启动'),
                    'status': 'running' if self.browser_manager.is_instance_running(instance['name']) else 'stopped',
                    'path': instance['path']
                }
                enhanced_instances.append(enhanced_instance)
            
            self.send_json_response({
                'success': True,
                'data': enhanced_instances,
                'total': len(enhanced_instances)
            })
        except Exception as e:
            self.send_json_response({'success': False, 'error': str(e)}, 500)
    
    def api_get_icons(self):
        """获取图标列表API"""
        try:
            icons = self.icon_manager.get_available_icons()
            self.send_json_response({'success': True, 'data': icons})
        except Exception as e:
            self.send_json_response({'success': False, 'error': str(e)}, 500)
    
    def api_get_groups(self):
        """获取分组列表API"""
        try:
            instances = self.browser_manager.list_instances()
            groups = set()
            for instance in instances:
                config = instance.get('config', {})
                group = config.get('group', '默认分组')
                groups.add(group)
            
            groups_list = list(groups) if groups else ['默认分组']
            self.send_json_response({'success': True, 'data': groups_list})
        except Exception as e:
            self.send_json_response({'success': False, 'error': str(e)}, 500)

    def api_test_chrome(self):
        """测试Chrome路径API"""
        try:
            chrome_path = self.browser_manager._find_chrome_executable()
            if chrome_path:
                import os
                exists = os.path.exists(chrome_path)
                self.send_json_response({
                    'success': True,
                    'data': {
                        'chrome_path': chrome_path,
                        'exists': exists,
                        'message': f'Chrome路径: {chrome_path}, 文件存在: {exists}'
                    }
                })
            else:
                self.send_json_response({
                    'success': False,
                    'error': '未找到Chrome可执行文件。请确保已安装Chrome浏览器。',
                    'data': {
                        'chrome_path': None,
                        'exists': False
                    }
                })
        except Exception as e:
            self.send_json_response({
                'success': False,
                'error': f'测试Chrome路径时发生错误: {str(e)}',
                'data': {
                    'chrome_path': None,
                    'exists': False
                }
            })

    def api_create_instance(self, data):
        """创建实例API"""
        try:
            name = data.get('name', '').strip()

            # 如果名称为空，生成默认名称
            if not name:
                name = self._generate_default_instance_name()

            # 检查名称是否已存在
            instances = self.browser_manager.list_instances()
            existing_names = [inst['name'] for inst in instances]
            if name in existing_names:
                self.send_json_response({'success': False, 'error': '实例名称已存在'}, 400)
                return

            # 获取其他参数
            display_name = data.get('display_name', '').strip()
            if not display_name:
                display_name = f'Chrome{name}' if name.startswith('浏览器') else f'Chrome - {name}'

            description = data.get('description', '').strip()
            if not description:
                description = display_name

            icon = data.get('icon', 'default')
            group = data.get('group', '默认分组')
            homepage = data.get('homepage', '')

            # 创建实例
            success = self.browser_manager.create_instance(
                instance_name=name,
                display_name=display_name,
                description=description,
                icon=icon,
                homepage=homepage,
                group=group
            )

            if success:
                self.send_json_response({'success': True, 'message': f'实例 "{name}" 创建成功'})
            else:
                self.send_json_response({'success': False, 'error': '创建实例失败'}, 500)

        except Exception as e:
            print(f"创建实例API错误: {e}")
            self.send_json_response({'success': False, 'error': str(e)}, 500)

    def _generate_default_instance_name(self):
        """生成默认实例名称"""
        try:
            instances = self.browser_manager.list_instances()
            existing_names = [inst['name'] for inst in instances]

            # 查找已存在的"浏览器X"格式的名称
            used_numbers = set()
            for name in existing_names:
                if name.startswith('浏览器') and len(name) > 3:
                    suffix = name[3:]  # 去掉"浏览器"前缀
                    try:
                        # 尝试解析数字
                        num = int(suffix)
                        used_numbers.add(num)
                    except ValueError:
                        continue

            # 找到第一个未使用的数字
            for i in range(1, 1000):  # 支持到999
                if i not in used_numbers:
                    return f'浏览器{i}'

            # 如果前999个都用完了，使用时间戳
            import time
            return f'浏览器{int(time.time())}'

        except Exception as e:
            print(f"生成默认名称失败: {e}")
            return '新浏览器实例'

    def api_update_instance(self, instance_name, data):
        """更新实例API"""
        try:
            # 检查实例是否存在
            instances = self.browser_manager.list_instances()
            existing_instance = None
            for inst in instances:
                if inst['name'] == instance_name:
                    existing_instance = inst
                    break

            if not existing_instance:
                self.send_json_response({'success': False, 'error': '实例不存在'}, 404)
                return

            # 更新配置
            config = existing_instance.get('config', {})
            config.update({
                'display_name': data.get('display_name', config.get('display_name', '')),
                'description': data.get('description', config.get('description', '')),
                'icon': data.get('icon', config.get('icon', 'default')),
                'group': data.get('group', config.get('group', '默认分组')),
                'updated_time': time.strftime('%Y-%m-%d %H:%M:%S')
            })

            # 如果要修改实例名称，需要特殊处理
            new_name = data.get('name', '').strip()
            if new_name and new_name != instance_name:
                # 检查新名称是否已存在
                existing_names = [inst['name'] for inst in instances]
                if new_name in existing_names:
                    self.send_json_response({'success': False, 'error': '新实例名称已存在'}, 400)
                    return

                # 重命名实例（这需要在browser_manager中实现）
                if hasattr(self.browser_manager, 'rename_instance'):
                    if self.browser_manager.rename_instance(instance_name, new_name):
                        instance_name = new_name
                    else:
                        self.send_json_response({'success': False, 'error': '重命名实例失败'}, 500)
                        return

            # 更新实例配置
            if self.browser_manager.update_instance(instance_name, config):
                self.send_json_response({'success': True, 'message': f'实例 "{instance_name}" 更新成功'})
            else:
                self.send_json_response({'success': False, 'error': '更新实例失败'}, 500)

        except Exception as e:
            self.send_json_response({'success': False, 'error': str(e)}, 500)

    def api_copy_instance(self, instance_name, data):
        """复制实例API"""
        try:
            # 检查源实例是否存在
            instances = self.browser_manager.list_instances()
            source_instance = None
            for inst in instances:
                if inst['name'] == instance_name:
                    source_instance = inst
                    break

            if not source_instance:
                self.send_json_response({'success': False, 'error': f'源实例 "{instance_name}" 不存在'}, 404)
                return

            # 生成新的实例名称
            base_name = data.get('name', instance_name)
            new_name = f"{base_name}_副本"
            counter = 1

            existing_names = [inst['name'] for inst in instances]
            while new_name in existing_names:
                counter += 1
                new_name = f"{base_name}_副本{counter}"

            # 准备新实例配置
            source_config = source_instance.get('config', {})
            new_config = {
                'display_name': data.get('display_name', f"{source_config.get('display_name', instance_name)} - 副本"),
                'description': data.get('description', f"复制自: {source_config.get('description', instance_name)}"),
                'icon': data.get('icon', source_config.get('icon', 'default')),
                'group': data.get('group', source_config.get('group', '默认分组'))
            }

            # 执行复制
            if self.browser_manager.copy_instance(instance_name, new_name, new_config):
                self.send_json_response({
                    'success': True,
                    'message': f'实例复制成功',
                    'data': {'name': new_name, 'display_name': new_config['display_name']}
                })
            else:
                self.send_json_response({'success': False, 'error': '复制实例失败'}, 500)

        except Exception as e:
            self.send_json_response({'success': False, 'error': str(e)}, 500)

    def api_delete_instance(self, instance_name):
        """删除实例API"""
        try:
            # 检查实例是否存在
            instances = self.browser_manager.list_instances()
            existing_names = [inst['name'] for inst in instances]
            if instance_name not in existing_names:
                self.send_json_response({'success': False, 'error': '实例不存在'}, 404)
                return

            # 检查实例是否正在运行
            if self.browser_manager.is_instance_running(instance_name):
                self.send_json_response({'success': False, 'error': '请先停止实例再删除'}, 400)
                return

            # 删除实例
            if self.browser_manager.delete_instance(instance_name):
                self.send_json_response({'success': True, 'message': f'实例 "{instance_name}" 删除成功'})
            else:
                self.send_json_response({'success': False, 'error': '删除实例失败'}, 500)

        except Exception as e:
            self.send_json_response({'success': False, 'error': str(e)}, 500)

    def api_launch_instance(self, instance_name):
        """启动实例API"""
        try:
            print(f"🚀 API收到启动请求: {instance_name}")
            sys.stdout.flush()

            # 检查Chrome可执行文件
            print("🔍 检查Chrome可执行文件...")
            sys.stdout.flush()
            chrome_path = self.browser_manager._find_chrome_executable()
            if not chrome_path:
                error_msg = "未找到Chrome可执行文件。请确保已安装Chrome浏览器或将Chrome便携版放在项目目录下。"
                print(f"❌ {error_msg}")
                sys.stdout.flush()
                self.send_json_response({'success': False, 'error': error_msg}, 500)
                return

            print(f"✅ 找到Chrome路径: {chrome_path}")
            sys.stdout.flush()

            # 调用启动方法
            print(f"📞 调用browser_manager.launch_instance({instance_name})")
            launch_result = self.browser_manager.launch_instance(instance_name)
            print(f"📋 启动结果: {launch_result}")

            if launch_result:
                # 更新最后启动时间
                print("⏰ 更新最后启动时间...")
                instances = self.browser_manager.list_instances()
                for inst in instances:
                    if inst['name'] == instance_name:
                        config = inst.get('config', {})
                        config['last_launch'] = time.strftime('%Y-%m-%d %H:%M:%S')
                        self.browser_manager.update_instance(instance_name, config)
                        break

                success_msg = f'实例 "{instance_name}" 启动成功'
                print(f"✅ {success_msg}")
                self.send_json_response({'success': True, 'message': success_msg})
            else:
                error_msg = f"启动实例失败。可能原因：1) Chrome路径不正确 2) 实例配置有误 3) 权限不足"
                print(f"❌ {error_msg}")
                self.send_json_response({'success': False, 'error': error_msg}, 500)
        except Exception as e:
            error_msg = f"启动实例时发生异常: {str(e)}"
            print(f"❌ {error_msg}")
            sys.stdout.flush()
            import traceback
            print("📋 完整错误堆栈:")
            traceback.print_exc()
            sys.stdout.flush()
            self.send_json_response({'success': False, 'error': error_msg}, 500)
    
    def api_stop_instance(self, instance_name):
        """停止实例API"""
        try:
            if self.browser_manager.stop_instance(instance_name):
                self.send_json_response({'success': True, 'message': f'实例 "{instance_name}" 停止成功'})
            else:
                self.send_json_response({'success': False, 'error': '停止实例失败'}, 500)
        except Exception as e:
            self.send_json_response({'success': False, 'error': str(e)}, 500)
    
    def send_json_response(self, data, status_code=200):
        """发送JSON响应"""
        response = json.dumps(data, ensure_ascii=False, indent=2)
        
        self.send_response(status_code)
        self.send_header('Content-type', 'application/json; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
        self.wfile.write(response.encode('utf-8'))

def setup_directories():
    """设置目录结构"""
    print("🔧 设置目录结构...")
    
    # 创建必要的目录
    directories = ['templates', 'static', 'static/css', 'static/js', 'static/images']
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    # 复制图标文件
    icons_dir = Path('icons')
    static_images_dir = Path('static/images')
    
    if icons_dir.exists():
        for icon_file in icons_dir.glob('*.ico'):
            dest_file = static_images_dir / icon_file.name
            if not dest_file.exists():
                import shutil
                shutil.copy2(icon_file, dest_file)
    
    print("✅ 目录结构设置完成")

def open_browser():
    """延迟打开浏览器"""
    time.sleep(2)
    webbrowser.open('http://127.0.0.1:8000')

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 启动现代化浏览器管理界面")
    print("=" * 60)
    
    # 设置目录结构
    setup_directories()
    
    # 检查必要文件
    required_files = ['templates/index.html', 'static/css/style.css', 'static/js/app.js']
    missing_files = []
    
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ 缺少必要文件:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        print("\n请确保所有文件都已创建")
        return
    
    print("📍 服务器地址: http://127.0.0.1:8000")
    print("🌐 正在启动Web服务器...")
    
    # 在新线程中打开浏览器
    threading.Thread(target=open_browser, daemon=True).start()
    
    # 启动HTTP服务器
    try:
        server = HTTPServer(('127.0.0.1', 8000), ModernBrowserHandler)
        print("✅ 服务器启动成功！")
        print("💡 按 Ctrl+C 停止服务器")
        server.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")

if __name__ == '__main__':
    main()
