#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试现代化界面功能
"""

import requests
import json
import time
import sys
from pathlib import Path

class WebInterfaceTest:
    """Web界面测试类"""
    
    def __init__(self, base_url="http://127.0.0.1:8000"):
        self.base_url = base_url
        self.test_results = []
    
    def log_test(self, test_name, success, message=""):
        """记录测试结果"""
        status = "✅ 通过" if success else "❌ 失败"
        result = f"{status} {test_name}"
        if message:
            result += f" - {message}"
        print(result)
        self.test_results.append((test_name, success, message))
    
    def test_api_instances_get(self):
        """测试获取实例列表API"""
        try:
            response = requests.get(f"{self.base_url}/api/instances", timeout=5)
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    count = len(data.get('data', []))
                    self.log_test("获取实例列表", True, f"找到 {count} 个实例")
                    return data.get('data', [])
                else:
                    self.log_test("获取实例列表", False, data.get('error', '未知错误'))
            else:
                self.log_test("获取实例列表", False, f"HTTP {response.status_code}")
        except Exception as e:
            self.log_test("获取实例列表", False, str(e))
        return []
    
    def test_api_icons_get(self):
        """测试获取图标列表API"""
        try:
            response = requests.get(f"{self.base_url}/api/icons", timeout=5)
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    count = len(data.get('data', []))
                    self.log_test("获取图标列表", True, f"找到 {count} 个图标")
                    return data.get('data', [])
                else:
                    self.log_test("获取图标列表", False, data.get('error', '未知错误'))
            else:
                self.log_test("获取图标列表", False, f"HTTP {response.status_code}")
        except Exception as e:
            self.log_test("获取图标列表", False, str(e))
        return []
    
    def test_api_groups_get(self):
        """测试获取分组列表API"""
        try:
            response = requests.get(f"{self.base_url}/api/groups", timeout=5)
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    count = len(data.get('data', []))
                    self.log_test("获取分组列表", True, f"找到 {count} 个分组")
                    return data.get('data', [])
                else:
                    self.log_test("获取分组列表", False, data.get('error', '未知错误'))
            else:
                self.log_test("获取分组列表", False, f"HTTP {response.status_code}")
        except Exception as e:
            self.log_test("获取分组列表", False, str(e))
        return []
    
    def test_create_instance(self):
        """测试创建实例"""
        test_data = {
            "name": "测试实例_" + str(int(time.time())),
            "display_name": "测试浏览器",
            "description": "这是一个测试实例",
            "icon": "default",
            "group": "测试分组"
        }
        
        try:
            response = requests.post(
                f"{self.base_url}/api/instances",
                json=test_data,
                timeout=10
            )
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    self.log_test("创建实例", True, f"实例 '{test_data['name']}' 创建成功")
                    return test_data['name']
                else:
                    self.log_test("创建实例", False, data.get('error', '未知错误'))
            else:
                self.log_test("创建实例", False, f"HTTP {response.status_code}")
        except Exception as e:
            self.log_test("创建实例", False, str(e))
        return None
    
    def test_delete_instance(self, instance_name):
        """测试删除实例"""
        if not instance_name:
            self.log_test("删除实例", False, "没有实例名称")
            return
        
        try:
            response = requests.delete(
                f"{self.base_url}/api/instances/{instance_name}",
                timeout=10
            )
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    self.log_test("删除实例", True, f"实例 '{instance_name}' 删除成功")
                else:
                    self.log_test("删除实例", False, data.get('error', '未知错误'))
            else:
                self.log_test("删除实例", False, f"HTTP {response.status_code}")
        except Exception as e:
            self.log_test("删除实例", False, str(e))
    
    def test_static_files(self):
        """测试静态文件访问"""
        static_files = [
            "/static/css/style.css",
            "/static/js/app.js"
        ]
        
        for file_path in static_files:
            try:
                response = requests.get(f"{self.base_url}{file_path}", timeout=5)
                if response.status_code == 200:
                    self.log_test(f"静态文件 {file_path}", True, f"大小: {len(response.content)} 字节")
                else:
                    self.log_test(f"静态文件 {file_path}", False, f"HTTP {response.status_code}")
            except Exception as e:
                self.log_test(f"静态文件 {file_path}", False, str(e))
    
    def test_main_page(self):
        """测试主页面"""
        try:
            response = requests.get(self.base_url, timeout=5)
            if response.status_code == 200:
                content = response.text
                # 检查关键元素
                key_elements = [
                    "浏览器管理",
                    "创建浏览器",
                    "批量操作",
                    "data-table"
                ]
                
                missing_elements = []
                for element in key_elements:
                    if element not in content:
                        missing_elements.append(element)
                
                if not missing_elements:
                    self.log_test("主页面", True, "所有关键元素都存在")
                else:
                    self.log_test("主页面", False, f"缺少元素: {', '.join(missing_elements)}")
            else:
                self.log_test("主页面", False, f"HTTP {response.status_code}")
        except Exception as e:
            self.log_test("主页面", False, str(e))
    
    def check_server_status(self):
        """检查服务器状态"""
        try:
            response = requests.get(self.base_url, timeout=3)
            return response.status_code == 200
        except:
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("=" * 60)
        print("🧪 现代化界面功能测试")
        print("=" * 60)
        
        # 检查服务器状态
        if not self.check_server_status():
            print("❌ 服务器未运行或无法访问")
            print("请先运行: python 启动现代化界面.py")
            return
        
        print("✅ 服务器运行正常")
        print()
        
        # 运行测试
        print("📋 API测试:")
        instances = self.test_api_instances_get()
        icons = self.test_api_icons_get()
        groups = self.test_api_groups_get()
        
        print("\n🌐 页面测试:")
        self.test_main_page()
        self.test_static_files()
        
        print("\n🔧 功能测试:")
        test_instance = self.test_create_instance()
        if test_instance:
            time.sleep(1)  # 等待创建完成
            self.test_delete_instance(test_instance)
        
        # 统计结果
        print("\n" + "=" * 60)
        print("📊 测试结果统计:")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for _, success, _ in self.test_results if success)
        failed_tests = total_tests - passed_tests
        
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests}")
        print(f"失败: {failed_tests}")
        print(f"成功率: {passed_tests/total_tests*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ 失败的测试:")
            for test_name, success, message in self.test_results:
                if not success:
                    print(f"  - {test_name}: {message}")
        
        print("\n" + "=" * 60)
        if failed_tests == 0:
            print("🎉 所有测试通过！现代化界面运行正常！")
        else:
            print("⚠️  部分测试失败，请检查相关功能")
        print("=" * 60)

def main():
    """主函数"""
    tester = WebInterfaceTest()
    tester.run_all_tests()

if __name__ == "__main__":
    main()
