{"test_summary": {"total_tests": 30, "passed_tests": 30, "failed_tests": 0, "success_rate": "100.0%"}, "test_results": [{"test_name": "Python版本检查", "success": true, "message": "Python 3.13", "timestamp": "2025-07-26T21:49:20.570884"}, {"test_name": "Chrome Portable检查", "success": true, "message": "找到Chrome: C:\\Users\\<USER>\\Downloads\\workspace\\browsers-v0.002\\GoogleChromePortable\\App\\Chrome-bin\\chrome.exe", "timestamp": "2025-07-26T21:49:20.571467"}, {"test_name": "目录检查 - tools", "success": true, "message": "目录存在: C:\\Users\\<USER>\\Downloads\\workspace\\browsers-v0.002\\tools", "timestamp": "2025-07-26T21:49:20.571882"}, {"test_name": "目录检查 - instances", "success": true, "message": "目录存在: C:\\Users\\<USER>\\Downloads\\workspace\\browsers-v0.002\\instances", "timestamp": "2025-07-26T21:49:20.572328"}, {"test_name": "目录检查 - icons", "success": true, "message": "目录存在: C:\\Users\\<USER>\\Downloads\\workspace\\browsers-v0.002\\icons", "timestamp": "2025-07-26T21:49:20.572672"}, {"test_name": "目录检查 - scripts", "success": true, "message": "目录存在: C:\\Users\\<USER>\\Downloads\\workspace\\browsers-v0.002\\scripts", "timestamp": "2025-07-26T21:49:20.572963"}, {"test_name": "文件检查 - tools/browser_manager.py", "success": true, "message": "文件存在", "timestamp": "2025-07-26T21:49:20.573493"}, {"test_name": "文件检查 - tools/icon_manager.py", "success": true, "message": "文件存在", "timestamp": "2025-07-26T21:49:20.573735"}, {"test_name": "文件检查 - tools/launcher_generator.py", "success": true, "message": "文件存在", "timestamp": "2025-07-26T21:49:20.573986"}, {"test_name": "文件检查 - tools/batch_configurator.py", "success": true, "message": "文件存在", "timestamp": "2025-07-26T21:49:20.574221"}, {"test_name": "文件检查 - tools/template_manager.py", "success": true, "message": "文件存在", "timestamp": "2025-07-26T21:49:20.574458"}, {"test_name": "文件检查 - tools/isolation_validator.py", "success": true, "message": "文件存在", "timestamp": "2025-07-26T21:49:20.574681"}, {"test_name": "文件检查 - scripts/launch_browser.py", "success": true, "message": "文件存在", "timestamp": "2025-07-26T21:49:20.574838"}, {"test_name": "文件检查 - README.md", "success": true, "message": "文件存在", "timestamp": "2025-07-26T21:49:20.574971"}, {"test_name": "文件检查 - docs/data_isolation.md", "success": true, "message": "文件存在", "timestamp": "2025-07-26T21:49:20.575098"}, {"test_name": "配置模板", "success": true, "message": "找到 11 个配置模板", "timestamp": "2025-07-26T21:49:20.575644"}, {"test_name": "创建实例", "success": true, "message": "成功创建实例: test_instance_1753537760", "timestamp": "2025-07-26T21:49:20.694360"}, {"test_name": "列出实例", "success": true, "message": "成功列出实例", "timestamp": "2025-07-26T21:49:20.796390"}, {"test_name": "生成图标", "success": true, "message": "成功生成工作主题图标", "timestamp": "2025-07-26T21:49:21.035194"}, {"test_name": "列出图标", "success": true, "message": "成功列出可用图标", "timestamp": "2025-07-26T21:49:21.175562"}, {"test_name": "设置图标", "success": true, "message": "成功为实例 test_instance_1753537760 设置图标", "timestamp": "2025-07-26T21:49:21.340199"}, {"test_name": "生成启动脚本", "success": true, "message": "成功为实例 test_instance_1753537760 生成启动脚本", "timestamp": "2025-07-26T21:49:21.441665"}, {"test_name": "Python启动脚本", "success": true, "message": "Python脚本存在: C:\\Users\\<USER>\\Downloads\\workspace\\browsers-v0.002\\instances\\test_instance_1753537760\\chrome_test_instance_1753537760.py", "timestamp": "2025-07-26T21:49:21.442050"}, {"test_name": "批处理启动脚本", "success": true, "message": "批处理脚本存在: C:\\Users\\<USER>\\Downloads\\workspace\\browsers-v0.002\\instances\\test_instance_1753537760\\启动_test_instance_1753537760.bat", "timestamp": "2025-07-26T21:49:21.442305"}, {"test_name": "全局启动器", "success": true, "message": "成功生成全局启动器", "timestamp": "2025-07-26T21:49:21.538871"}, {"test_name": "列出配置模板", "success": true, "message": "成功列出配置模板", "timestamp": "2025-07-26T21:49:21.644412"}, {"test_name": "预览配置模板", "success": true, "message": "成功预览basic_set.json", "timestamp": "2025-07-26T21:49:21.733065"}, {"test_name": "列出模板", "success": true, "message": "成功列出所有模板", "timestamp": "2025-07-26T21:49:21.829641"}, {"test_name": "数据隔离验证", "success": true, "message": "数据隔离验证通过", "timestamp": "2025-07-26T21:49:21.928171"}, {"test_name": "清理实例 - test_instance_1753537760", "success": true, "message": "成功删除测试实例", "timestamp": "2025-07-26T21:49:22.019923"}], "generated_at": "2025-07-26T21:49:22.020216"}