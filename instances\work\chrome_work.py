#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Chrome - 工作 启动脚本
自动生成，请勿手动修改
实例: work
生成时间: 2025-07-26 21:16:43
"""

import os
import sys
import subprocess
from pathlib import Path


def check_chrome_portable():
    """检查Chrome Portable是否存在"""
    script_dir = Path(__file__).parent
    base_dir = script_dir.parent.parent
    chrome_exe = base_dir / "GoogleChromePortable" / "App" / "Chrome-bin" / "chrome.exe"
    
    if not chrome_exe.exists():
        print(f"❌ Chrome Portable不存在: {chrome_exe}")
        print("请确保GoogleChromePortable目录在项目根目录中")
        return False
    return True


def check_data_directory():
    """检查数据目录是否存在"""
    script_dir = Path(__file__).parent
    data_dir = script_dir / "Data" / "profile"
    
    if not data_dir.exists():
        print(f"❌ 数据目录不存在: {data_dir}")
        print("请重新创建实例或检查数据目录")
        return False
    return True


def main():
    """主启动函数"""
    print(f"🚀 启动 Chrome - 工作...")
    
    # 检查依赖
    if not check_chrome_portable():
        input("按回车键退出...")
        return
    
    if not check_data_directory():
        input("按回车键退出...")
        return
    
    # 获取路径
    script_dir = Path(__file__).parent
    base_dir = script_dir.parent.parent
    chrome_exe = base_dir / "GoogleChromePortable" / "App" / "Chrome-bin" / "chrome.exe"
    user_data_dir = script_dir / "Data" / "profile"
    
    # 构建启动命令
    cmd = [
        str(chrome_exe),
        f"--user-data-dir={user_data_dir}",
        "--no-first-run",
        "--no-default-browser-check",
        "--disable-default-apps",
        "--disable-background-mode",
        "https://www.google.com",
    ]
    
    # 启动Chrome
    try:
        # 使用subprocess.Popen启动，不等待进程结束
        process = subprocess.Popen(cmd, 
                                 stdout=subprocess.DEVNULL, 
                                 stderr=subprocess.DEVNULL)
        print(f"✅ Chrome - 工作 启动成功 (PID: {process.pid})")
        
    except FileNotFoundError:
        print("❌ Chrome可执行文件未找到")
        print("请检查GoogleChromePortable是否正确安装")
        input("按回车键退出...")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        input("按回车键退出...")


if __name__ == "__main__":
    main()
