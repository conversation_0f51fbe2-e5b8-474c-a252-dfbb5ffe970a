#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI功能Bug检查和修复脚本
系统性检查GUI功能中的潜在问题
"""

import sys
import os
import time
import traceback
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def check_gui_launcher():
    """检查GUI启动器"""
    print("🔍 检查GUI启动器...")
    
    issues = []
    
    try:
        from gui_launcher import BrowserLauncherGUI, ProcessMonitor, InstanceCard
        
        # 检查类定义
        print("✓ 类定义正常")
        
        # 检查初始化
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()
        
        try:
            app = BrowserLauncherGUI()
            app.root.withdraw()
            print("✓ GUI初始化正常")
            
            # 检查关键方法
            methods_to_check = [
                'setup_window', 'setup_managers', 'setup_ui', 
                'load_instances', 'refresh_instances', 'launch_instance'
            ]
            
            for method in methods_to_check:
                if hasattr(app, method):
                    print(f"✓ 方法存在: {method}")
                else:
                    issues.append(f"缺少方法: {method}")
            
            app.root.destroy()
            
        except Exception as e:
            issues.append(f"GUI初始化失败: {e}")
            
        root.destroy()
        
    except ImportError as e:
        issues.append(f"导入失败: {e}")
    except Exception as e:
        issues.append(f"未知错误: {e}")
    
    return issues

def check_gui_dialogs():
    """检查GUI对话框"""
    print("🔍 检查GUI对话框...")
    
    issues = []
    
    try:
        from gui_dialogs import NewInstanceDialog, BatchConfigDialog
        
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()
        
        # 检查新建实例对话框
        try:
            dialog = NewInstanceDialog(root)
            dialog.dialog.withdraw()
            
            # 检查关键属性
            required_attrs = ['name_var', 'display_var', 'desc_var', 'homepage_var', 'icon_var']
            for attr in required_attrs:
                if hasattr(dialog, attr):
                    print(f"✓ 对话框属性存在: {attr}")
                else:
                    issues.append(f"新建对话框缺少属性: {attr}")
            
            dialog.dialog.destroy()
            print("✓ 新建实例对话框正常")
            
        except Exception as e:
            issues.append(f"新建实例对话框错误: {e}")
        
        # 检查批量配置对话框
        try:
            dialog = BatchConfigDialog(root)
            dialog.dialog.withdraw()
            dialog.dialog.destroy()
            print("✓ 批量配置对话框正常")
            
        except Exception as e:
            issues.append(f"批量配置对话框错误: {e}")
        
        root.destroy()
        
    except ImportError as e:
        issues.append(f"对话框导入失败: {e}")
    except Exception as e:
        issues.append(f"对话框未知错误: {e}")
    
    return issues

def check_process_monitor():
    """检查进程监控"""
    print("🔍 检查进程监控...")
    
    issues = []
    
    try:
        from gui_launcher import ProcessMonitor
        
        monitor = ProcessMonitor()
        
        # 测试基本功能
        try:
            running = monitor.is_instance_running("test")
            print(f"✓ 实例状态检查: {running}")
        except Exception as e:
            issues.append(f"实例状态检查失败: {e}")
        
        try:
            instances = monitor.get_running_instances()
            print(f"✓ 运行实例获取: {len(instances)} 个")
        except Exception as e:
            issues.append(f"运行实例获取失败: {e}")
        
    except Exception as e:
        issues.append(f"进程监控错误: {e}")
    
    return issues

def check_file_integrity():
    """检查文件完整性"""
    print("🔍 检查文件完整性...")
    
    issues = []
    
    # 检查关键文件
    critical_files = {
        'gui_launcher.py': '主GUI文件',
        'gui_dialogs.py': '对话框文件',
        '启动GUI界面.py': 'Python启动器',
        '启动GUI界面.bat': '批处理启动器'
    }
    
    for filename, description in critical_files.items():
        filepath = Path(filename)
        if not filepath.exists():
            issues.append(f"缺少文件: {filename} ({description})")
        else:
            # 检查文件大小
            size = filepath.stat().st_size
            if size < 100:  # 文件太小可能有问题
                issues.append(f"文件可能损坏: {filename} (大小: {size} bytes)")
            else:
                print(f"✓ 文件正常: {filename}")
    
    return issues

def check_dependencies():
    """检查依赖库"""
    print("🔍 检查依赖库...")
    
    issues = []
    
    dependencies = [
        ('tkinter', 'GUI框架'),
        ('PIL', '图像处理'),
        ('psutil', '进程监控'),
        ('json', 'JSON处理'),
        ('threading', '多线程'),
        ('subprocess', '进程管理')
    ]
    
    for module, desc in dependencies:
        try:
            if module == 'PIL':
                from PIL import Image, ImageTk
            else:
                __import__(module)
            print(f"✓ {module} - {desc}")
        except ImportError:
            issues.append(f"缺少依赖: {module} ({desc})")
    
    return issues

def check_tools_integration():
    """检查工具集成"""
    print("🔍 检查工具集成...")
    
    issues = []
    
    try:
        sys.path.append(str(Path(__file__).parent / "tools"))
        
        # 检查核心工具模块
        tools = [
            ('browser_manager', 'BrowserManager'),
            ('icon_manager', 'IconManager'),
            ('batch_configurator', 'BatchConfigurator')
        ]
        
        for module_name, class_name in tools:
            try:
                module = __import__(module_name)
                if hasattr(module, class_name):
                    print(f"✓ 工具模块正常: {module_name}.{class_name}")
                else:
                    issues.append(f"工具模块缺少类: {module_name}.{class_name}")
            except ImportError as e:
                issues.append(f"工具模块导入失败: {module_name} - {e}")
    
    except Exception as e:
        issues.append(f"工具集成检查失败: {e}")
    
    return issues

def test_actual_functionality():
    """测试实际功能"""
    print("🔍 测试实际功能...")
    
    issues = []
    
    try:
        # 测试创建临时实例
        sys.path.append(str(Path(__file__).parent / "tools"))
        from browser_manager import BrowserManager
        
        manager = BrowserManager()
        
        # 测试列出实例
        try:
            instances = manager.list_instances()
            print(f"✓ 实例列表获取: {len(instances)} 个实例")
        except Exception as e:
            issues.append(f"实例列表获取失败: {e}")
        
        # 测试创建测试实例
        test_name = "gui_test_temp"
        try:
            # 先删除可能存在的测试实例
            try:
                manager.delete_instance(test_name)
            except:
                pass
            
            # 创建测试实例
            success = manager.create_instance(
                test_name,
                display_name="GUI测试实例",
                description="临时测试实例"
            )
            
            if success:
                print("✓ 实例创建功能正常")
                
                # 测试删除
                delete_success = manager.delete_instance(test_name)
                if delete_success:
                    print("✓ 实例删除功能正常")
                else:
                    issues.append("实例删除功能失败")
            else:
                issues.append("实例创建功能失败")
                
        except Exception as e:
            issues.append(f"实例管理功能测试失败: {e}")
    
    except Exception as e:
        issues.append(f"功能测试失败: {e}")
    
    return issues

def main():
    """主检查函数"""
    print("=" * 60)
    print("🔧 GUI功能Bug检查器")
    print("=" * 60)
    
    all_issues = []
    
    # 执行各项检查
    checks = [
        ("文件完整性", check_file_integrity),
        ("依赖库", check_dependencies),
        ("工具集成", check_tools_integration),
        ("进程监控", check_process_monitor),
        ("GUI启动器", check_gui_launcher),
        ("GUI对话框", check_gui_dialogs),
        ("实际功能", test_actual_functionality)
    ]
    
    for check_name, check_func in checks:
        print(f"\n{'='*20} {check_name} {'='*20}")
        try:
            issues = check_func()
            if issues:
                print(f"❌ 发现 {len(issues)} 个问题:")
                for issue in issues:
                    print(f"   • {issue}")
                all_issues.extend(issues)
            else:
                print("✅ 检查通过")
        except Exception as e:
            error_msg = f"{check_name}检查异常: {e}"
            print(f"❌ {error_msg}")
            all_issues.append(error_msg)
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 检查结果总结")
    print("=" * 60)
    
    if all_issues:
        print(f"❌ 发现 {len(all_issues)} 个问题需要修复:")
        for i, issue in enumerate(all_issues, 1):
            print(f"{i:2d}. {issue}")
        
        print(f"\n🔧 建议修复措施:")
        print("1. 检查文件完整性，重新下载缺失文件")
        print("2. 安装缺失的依赖库: pip install pillow psutil")
        print("3. 检查Python版本兼容性")
        print("4. 查看详细错误日志进行针对性修复")
        
        return False
    else:
        print("🎉 所有检查通过！GUI功能正常。")
        return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            print("\n按回车键退出...")
            input()
    except KeyboardInterrupt:
        print("\n\n检查被中断。")
    except Exception as e:
        print(f"\n检查过程中出现异常: {e}")
        traceback.print_exc()
        input("按回车键退出...")
