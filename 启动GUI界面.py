#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器多账号绿色版 - GUI界面启动器
双击此文件启动图形化界面
"""

import sys
import os
from pathlib import Path

# 确保当前目录在Python路径中
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

try:
    # 导入并启动GUI
    from gui_launcher import main
    
    if __name__ == "__main__":
        print("正在启动浏览器多账号绿色版图形界面...")
        main()
        
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保所有必要的文件都在当前目录中")
    input("按回车键退出...")
    
except Exception as e:
    print(f"启动失败: {e}")
    input("按回车键退出...")
