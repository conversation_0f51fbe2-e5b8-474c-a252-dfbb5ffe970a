<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>浏览器管理 - 现代化界面</title>
    <link rel="stylesheet" href="/static/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- 侧边导航栏 -->
    <div class="sidebar">
        <div class="sidebar-header">
            <i class="fas fa-globe"></i>
            <span>浏览器</span>
        </div>
        
        <nav class="sidebar-nav">
            <ul>
                <li class="nav-item active">
                    <a href="#" data-page="browsers">
                        <i class="fas fa-list"></i>
                        <span>列表</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" data-page="groups">
                        <i class="fas fa-folder"></i>
                        <span>分组管理</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" data-page="domains">
                        <i class="fas fa-globe-americas"></i>
                        <span>域名管理</span>
                    </a>
                </li>
            </ul>
        </nav>
    </div>

    <!-- 主内容区 -->
    <div class="main-content">
        <!-- 顶部工具栏 -->
        <div class="toolbar">
            <div class="toolbar-left">
                <button class="btn btn-primary" id="createBrowserBtn">
                    <i class="fas fa-plus"></i>
                    创建浏览器
                </button>
                
                <div class="dropdown">
                    <button class="btn btn-secondary dropdown-toggle" id="batchOperationBtn">
                        <i class="fas fa-tasks"></i>
                        批量操作
                    </button>
                    <div class="dropdown-menu" id="batchMenu">
                        <a href="#" class="dropdown-item" data-action="batch-start">批量启动</a>
                        <a href="#" class="dropdown-item" data-action="batch-stop">批量停止</a>
                        <a href="#" class="dropdown-item" data-action="batch-delete">批量删除</a>
                    </div>
                </div>
                
                <button class="btn btn-info" id="syncBtn">
                    <i class="fas fa-sync"></i>
                    同步
                </button>
            </div>
            
            <div class="toolbar-right">
                <div class="search-box">
                    <input type="text" placeholder="搜索" id="searchInput">
                    <i class="fas fa-search"></i>
                </div>
                
                <button class="btn btn-outline" id="loginBtn">
                    <i class="fas fa-user"></i>
                    登录
                </button>
                
                <button class="btn btn-outline" id="importBtn">
                    <i class="fas fa-download"></i>
                    导入
                </button>
                
                <button class="btn btn-outline" id="exportBtn">
                    <i class="fas fa-upload"></i>
                    导出
                </button>
            </div>
        </div>

        <!-- 表格容器 -->
        <div class="table-container">
            <table class="data-table" id="browsersTable">
                <thead>
                    <tr>
                        <th width="60">
                            <input type="checkbox" id="selectAll">
                        </th>
                        <th width="80">序号</th>
                        <th width="150">名称</th>
                        <th width="120">分组</th>
                        <th width="80">图标</th>
                        <th width="200">备注</th>
                        <th width="180">最后启动时间</th>
                        <th width="100">启动</th>
                        <th width="150">操作</th>
                    </tr>
                </thead>
                <tbody id="browsersTableBody">
                    <!-- 数据将通过JavaScript动态加载 -->
                </tbody>
            </table>
        </div>

        <!-- 分页控件 -->
        <div class="pagination-container">
            <div class="pagination-info">
                共 <span id="totalCount">0</span> 条，<span id="pageSize">20</span>条/页
            </div>
            
            <div class="pagination">
                <button class="page-btn" id="prevPage" disabled>
                    <i class="fas fa-chevron-left"></i>
                </button>
                
                <div class="page-numbers" id="pageNumbers">
                    <button class="page-btn active">1</button>
                </div>
                
                <button class="page-btn" id="nextPage" disabled>
                    <i class="fas fa-chevron-right"></i>
                </button>
                
                <div class="page-jump">
                    前往 <input type="number" id="jumpPage" min="1" value="1"> 页
                </div>
            </div>
        </div>
    </div>

    <!-- 创建/编辑浏览器模态框 -->
    <div class="modal" id="browserModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">创建浏览器</h3>
                <button class="modal-close" id="modalClose">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <div class="modal-body">
                <form id="browserForm">
                    <div class="form-group">
                        <label for="browserName">实例名称 *</label>
                        <input type="text" id="browserName" name="name" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="displayName">显示名称</label>
                        <input type="text" id="displayName" name="display_name">
                    </div>
                    
                    <div class="form-group">
                        <label for="browserGroup">分组</label>
                        <select id="browserGroup" name="group">
                            <option value="默认分组">默认分组</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="browserIcon">图标</label>
                        <select id="browserIcon" name="icon">
                            <option value="default">默认</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="browserDescription">备注</label>
                        <textarea id="browserDescription" name="description" rows="3"></textarea>
                    </div>
                </form>
            </div>
            
            <div class="modal-footer">
                <button class="btn btn-secondary" id="modalCancel">取消</button>
                <button class="btn btn-primary" id="modalSave">保存</button>
            </div>
        </div>
    </div>

    <!-- 确认删除模态框 -->
    <div class="modal" id="confirmModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>确认删除</h3>
                <button class="modal-close" id="confirmModalClose">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <div class="modal-body">
                <p id="confirmMessage">确定要删除这个浏览器实例吗？</p>
            </div>
            
            <div class="modal-footer">
                <button class="btn btn-secondary" id="confirmCancel">取消</button>
                <button class="btn btn-danger" id="confirmDelete">删除</button>
            </div>
        </div>
    </div>

    <!-- 加载提示 -->
    <div class="loading" id="loading">
        <div class="loading-spinner"></div>
        <div class="loading-text">加载中...</div>
    </div>

    <!-- 消息提示 -->
    <div class="toast" id="toast">
        <div class="toast-content">
            <i class="toast-icon"></i>
            <span class="toast-message"></span>
        </div>
    </div>

    <script src="/static/js/app.js"></script>
</body>
</html>
