#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量配置工具
支持批量创建和配置多个浏览器实例
"""

import os
import json
import subprocess
from pathlib import Path
from datetime import datetime


class BatchConfigurator:
    """批量配置器"""
    
    def __init__(self, base_dir=None):
        """初始化批量配置器"""
        if base_dir is None:
            self.base_dir = Path(__file__).parent.parent
        else:
            self.base_dir = Path(base_dir)
            
        self.tools_dir = self.base_dir / "tools"
        self.templates_dir = self.tools_dir / "config_templates"
        
        # 确保模板目录存在
        self.templates_dir.mkdir(exist_ok=True)
    
    def load_batch_config(self, config_file):
        """加载批量配置文件"""
        try:
            config_path = Path(config_file)
            if not config_path.exists():
                # 尝试在模板目录中查找
                config_path = self.templates_dir / config_file
                if not config_path.exists():
                    print(f"配置文件不存在: {config_file}")
                    return None

            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)

            print(f"已加载配置文件: {config_path}")
            return config

        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return None
    
    def create_instances_from_config(self, config):
        """根据配置批量创建实例"""
        if not config or "instances" not in config:
            print("无效的配置文件格式")
            return False

        instances = config["instances"]
        success_count = 0
        total_count = len(instances)

        print(f"开始批量创建 {total_count} 个实例...")

        for i, instance_config in enumerate(instances, 1):
            instance_name = instance_config.get("name")
            if not instance_name:
                print(f"实例 {i} 缺少名称，跳过")
                continue

            print(f"\n创建实例 {i}/{total_count}: {instance_name}")

            if self.create_single_instance(instance_config):
                success_count += 1
                print(f"实例 {instance_name} 创建成功")
            else:
                print(f"实例 {instance_name} 创建失败")

        print(f"\n批量创建完成: {success_count}/{total_count} 个实例成功创建")
        return success_count == total_count
    
    def create_single_instance(self, instance_config):
        """创建单个实例"""
        try:
            name = instance_config.get("name")
            display_name = instance_config.get("display_name", name)
            description = instance_config.get("description", "")
            homepage = instance_config.get("homepage", "")
            icon = instance_config.get("icon", "default")
            
            # 构建browser_manager命令
            cmd = [
                "python", 
                str(self.tools_dir / "browser_manager.py"),
                "create",
                name,
                "--display-name", display_name,
                "--description", description
            ]
            
            if homepage:
                cmd.extend(["--homepage", homepage])
            
            # 执行创建命令
            result = subprocess.run(cmd, 
                                  capture_output=True, 
                                  text=True, 
                                  cwd=str(self.base_dir))
            
            if result.returncode != 0:
                print(f"创建实例失败: {result.stderr}")
                return False
            
            # 设置图标
            if icon and icon != "default":
                icon_cmd = [
                    "python",
                    str(self.tools_dir / "icon_manager.py"),
                    "set",
                    name,
                    icon
                ]
                
                icon_result = subprocess.run(icon_cmd,
                                           capture_output=True,
                                           text=True,
                                           cwd=str(self.base_dir))
                
                if icon_result.returncode != 0:
                    print(f"⚠️ 设置图标失败: {icon_result.stderr}")
            
            # 生成启动脚本
            launcher_cmd = [
                "python",
                str(self.tools_dir / "launcher_generator.py"),
                "generate",
                name
            ]
            
            launcher_result = subprocess.run(launcher_cmd,
                                           capture_output=True,
                                           text=True,
                                           cwd=str(self.base_dir))
            
            if launcher_result.returncode != 0:
                print(f"⚠️ 生成启动脚本失败: {launcher_result.stderr}")
            
            return True
            
        except Exception as e:
            print(f"创建实例异常: {e}")
            return False
    
    def create_default_batch_configs(self):
        """创建默认的批量配置模板"""
        configs = {
            "basic_set.json": {
                "name": "基础实例集合",
                "description": "包含工作、个人、购物三个基础实例",
                "instances": [
                    {
                        "name": "work",
                        "display_name": "Chrome - 工作",
                        "description": "工作专用浏览器",
                        "homepage": "https://www.google.com",
                        "icon": "work"
                    },
                    {
                        "name": "personal",
                        "display_name": "Chrome - 个人",
                        "description": "个人使用浏览器",
                        "homepage": "https://www.baidu.com",
                        "icon": "personal"
                    },
                    {
                        "name": "shopping",
                        "display_name": "Chrome - 购物",
                        "description": "购物专用浏览器",
                        "homepage": "https://www.taobao.com",
                        "icon": "shopping"
                    }
                ]
            },
            "complete_set.json": {
                "name": "完整实例集合",
                "description": "包含多种用途的完整实例集合",
                "instances": [
                    {
                        "name": "work",
                        "display_name": "Chrome - 工作",
                        "description": "工作专用浏览器",
                        "homepage": "https://www.google.com",
                        "icon": "work"
                    },
                    {
                        "name": "personal",
                        "display_name": "Chrome - 个人",
                        "description": "个人使用浏览器",
                        "homepage": "https://www.baidu.com",
                        "icon": "personal"
                    },
                    {
                        "name": "shopping",
                        "display_name": "Chrome - 购物",
                        "description": "购物专用浏览器",
                        "homepage": "https://www.taobao.com",
                        "icon": "shopping"
                    },
                    {
                        "name": "social",
                        "display_name": "Chrome - 社交",
                        "description": "社交媒体专用浏览器",
                        "homepage": "https://www.weibo.com",
                        "icon": "social"
                    },
                    {
                        "name": "dev",
                        "display_name": "Chrome - 开发",
                        "description": "开发测试专用浏览器",
                        "homepage": "https://github.com",
                        "icon": "dev"
                    },
                    {
                        "name": "finance",
                        "display_name": "Chrome - 金融",
                        "description": "金融理财专用浏览器",
                        "homepage": "https://www.alipay.com",
                        "icon": "finance"
                    }
                ]
            },
            "gaming_set.json": {
                "name": "游戏实例集合",
                "description": "专为游戏多开设计的实例集合",
                "instances": [
                    {
                        "name": "game1",
                        "display_name": "Chrome - 游戏1",
                        "description": "游戏账号1专用浏览器",
                        "homepage": "",
                        "icon": "gaming"
                    },
                    {
                        "name": "game2",
                        "display_name": "Chrome - 游戏2",
                        "description": "游戏账号2专用浏览器",
                        "homepage": "",
                        "icon": "gaming"
                    },
                    {
                        "name": "game3",
                        "display_name": "Chrome - 游戏3",
                        "description": "游戏账号3专用浏览器",
                        "homepage": "",
                        "icon": "gaming"
                    }
                ]
            }
        }
        
        created_count = 0
        for filename, config in configs.items():
            config_path = self.templates_dir / filename
            try:
                with open(config_path, 'w', encoding='utf-8') as f:
                    json.dump(config, f, ensure_ascii=False, indent=2)
                print(f"已创建配置模板: {config_path}")
                created_count += 1
            except Exception as e:
                print(f"创建配置模板失败 {filename}: {e}")

        print(f"成功创建 {created_count}/{len(configs)} 个配置模板")
        return created_count
    
    def list_available_configs(self):
        """列出可用的配置模板"""
        if not self.templates_dir.exists():
            print("配置模板目录不存在")
            return []
        
        configs = []
        for config_file in self.templates_dir.glob("*.json"):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                configs.append({
                    "file": config_file.name,
                    "path": str(config_file),
                    "name": config.get("name", config_file.stem),
                    "description": config.get("description", ""),
                    "instance_count": len(config.get("instances", []))
                })
            except Exception as e:
                print(f"⚠️ 读取配置文件失败 {config_file.name}: {e}")
        
        return configs
    
    def preview_config(self, config_file):
        """预览配置文件内容"""
        config = self.load_batch_config(config_file)
        if not config:
            return False
        
        print(f"\n配置预览: {config.get('name', config_file)}")
        print("=" * 50)
        print(f"描述: {config.get('description', '无')}")
        
        instances = config.get("instances", [])
        print(f"实例数量: {len(instances)}")
        
        if instances:
            print("\n实例列表:")
            for i, instance in enumerate(instances, 1):
                name = instance.get("name", "未命名")
                display_name = instance.get("display_name", name)
                description = instance.get("description", "无描述")
                homepage = instance.get("homepage", "无")
                icon = instance.get("icon", "default")
                
                print(f"  {i}. {display_name} ({name})")
                print(f"     描述: {description}")
                print(f"     主页: {homepage}")
                print(f"     图标: {icon}")
                print()
        
        return True


def main():
    """命令行接口"""
    import argparse
    
    parser = argparse.ArgumentParser(description="批量配置工具")
    subparsers = parser.add_subparsers(dest="command", help="可用命令")
    
    # 创建默认配置模板
    create_parser = subparsers.add_parser("create-templates", help="创建默认配置模板")
    
    # 列出可用配置
    list_parser = subparsers.add_parser("list", help="列出可用配置模板")
    
    # 预览配置
    preview_parser = subparsers.add_parser("preview", help="预览配置文件")
    preview_parser.add_argument("config", help="配置文件名")
    
    # 批量创建实例
    batch_parser = subparsers.add_parser("batch", help="批量创建实例")
    batch_parser.add_argument("config", help="配置文件名")
    batch_parser.add_argument("--preview", action="store_true", help="仅预览，不实际创建")
    
    args = parser.parse_args()
    
    configurator = BatchConfigurator()
    
    if args.command == "create-templates":
        configurator.create_default_batch_configs()
    
    elif args.command == "list":
        configs = configurator.list_available_configs()
        if configs:
            print("可用配置模板:")
            for config in configs:
                print(f"  - {config['file']}")
                print(f"    名称: {config['name']}")
                print(f"    描述: {config['description']}")
                print(f"    实例数: {config['instance_count']}")
                print()
        else:
            print("没有找到配置模板")
            print("运行 'python batch_configurator.py create-templates' 创建默认模板")
    
    elif args.command == "preview":
        configurator.preview_config(args.config)
    
    elif args.command == "batch":
        config = configurator.load_batch_config(args.config)
        if config:
            if args.preview:
                configurator.preview_config(args.config)
            else:
                configurator.create_instances_from_config(config)
    
    else:
        parser.print_help()


if __name__ == "__main__":
    main()
