#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终验证脚本 - 确认实例显示问题已修复
"""

import sys
import os
import time
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))
sys.path.append(str(Path(__file__).parent / "tools"))

def test_instance_loading():
    """测试实例加载功能"""
    print("🔍 测试实例加载功能...")
    
    try:
        from browser_manager import BrowserManager
        
        manager = BrowserManager()
        instances = manager.list_instances()
        
        print(f"✅ 成功获取 {len(instances)} 个实例:")
        for i, instance in enumerate(instances, 1):
            config = instance.get('config', {})
            display_name = config.get('display_name', instance['name'])
            description = config.get('description', '无描述')
            print(f"   {i}. {instance['name']} - {display_name}")
            print(f"      描述: {description}")
        
        return len(instances) > 0
        
    except Exception as e:
        print(f"❌ 实例加载测试失败: {e}")
        return False

def test_gui_components():
    """测试GUI组件"""
    print("\n🔍 测试GUI组件...")
    
    try:
        import tkinter as tk
        from gui_launcher import BrowserLauncherGUI, InstanceCard
        
        # 测试主界面创建
        root = tk.Tk()
        root.withdraw()
        
        app = BrowserLauncherGUI()
        app.root.withdraw()
        
        print("✅ GUI主界面创建成功")
        
        # 检查关键组件
        components = [
            ('main_frame', '主框架'),
            ('canvas', '画布'),
            ('scrollable_frame', '滚动框架'),
            ('instance_cards', '实例卡片字典')
        ]
        
        for attr, name in components:
            if hasattr(app, attr):
                print(f"✅ {name} 存在")
            else:
                print(f"❌ {name} 缺失")
                return False
        
        # 检查实例卡片数量
        card_count = len(app.instance_cards)
        print(f"✅ 实例卡片数量: {card_count}")
        
        app.root.destroy()
        root.destroy()
        
        return card_count > 0
        
    except Exception as e:
        print(f"❌ GUI组件测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_instance_card_creation():
    """测试实例卡片创建"""
    print("\n🔍 测试实例卡片创建...")
    
    try:
        import tkinter as tk
        from gui_launcher import InstanceCard
        
        root = tk.Tk()
        root.withdraw()
        
        # 创建测试框架
        test_frame = tk.Frame(root)
        
        # 测试实例数据
        test_instance = {
            'name': 'test_verification',
            'config': {
                'display_name': '验证测试实例',
                'description': '用于验证的测试实例',
                'icon': 'default'
            }
        }
        
        # 创建实例卡片
        card = InstanceCard(
            test_frame,
            test_instance,
            on_launch=lambda x: print(f"测试启动: {x}"),
            on_menu=lambda x: print(f"测试菜单: {x}")
        )
        
        print("✅ 实例卡片创建成功")
        
        # 检查卡片属性
        if hasattr(card, 'instance_data'):
            print("✅ 实例数据绑定正常")
        else:
            print("❌ 实例数据绑定失败")
            return False
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 实例卡片创建测试失败: {e}")
        return False

def test_scrollable_frame():
    """测试滚动框架配置"""
    print("\n🔍 测试滚动框架配置...")
    
    try:
        import tkinter as tk
        from tkinter import ttk
        
        root = tk.Tk()
        root.withdraw()
        
        # 模拟GUI的滚动框架创建
        main_container = tk.Frame(root, bg='#FAFAFA')
        canvas = tk.Canvas(main_container, bg='#FAFAFA', highlightthickness=0)
        scrollbar = ttk.Scrollbar(main_container, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg='#FAFAFA')
        
        # 配置滚动
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas_window = canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        print("✅ 滚动框架配置成功")
        
        # 测试添加内容
        for i in range(3):
            test_label = tk.Label(
                scrollable_frame,
                text=f"测试内容 {i+1}",
                bg='white',
                relief='raised',
                bd=1,
                height=3
            )
            test_label.pack(fill='x', padx=10, pady=5)
        
        print("✅ 滚动内容添加成功")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 滚动框架测试失败: {e}")
        return False

def main():
    """主验证函数"""
    print("=" * 60)
    print("🔧 实例显示问题修复验证")
    print("=" * 60)
    
    tests = [
        ("实例加载功能", test_instance_loading),
        ("GUI组件", test_gui_components),
        ("实例卡片创建", test_instance_card_creation),
        ("滚动框架配置", test_scrollable_frame)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*15} {test_name} {'='*15}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 60)
    print("📊 验证结果")
    print("=" * 60)
    
    if passed == total:
        print("🎉 所有验证通过！实例显示问题已修复！")
        print("\n✅ 修复内容:")
        print("• 修复了滚动框架配置问题")
        print("• 改进了canvas和scrollbar的布局")
        print("• 添加了强制界面更新")
        print("• 确保实例卡片正确显示")
        
        print("\n🚀 GUI界面现在应该能正常显示所有浏览器实例！")
        
        return True
    else:
        print(f"⚠️ {total - passed}/{total} 个验证失败")
        print("需要进一步检查GUI配置")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🎯 建议:")
            print("1. 重新启动GUI界面查看效果")
            print("2. 测试新建实例功能")
            print("3. 测试实例启动功能")
    except Exception as e:
        print(f"\n验证异常: {e}")
    
    print("\n按回车键退出...")
    input()
