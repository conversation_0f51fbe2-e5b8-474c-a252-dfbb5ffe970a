#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器多账号绿色版 - 图形化启动器
主界面程序，提供友好的GUI界面来管理和启动浏览器实例
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
import sys
import json
import threading
import time
import subprocess
from pathlib import Path
from PIL import Image, ImageTk
import psutil

# 添加tools目录到Python路径
sys.path.append(str(Path(__file__).parent / "tools"))

try:
    from browser_manager import BrowserManager
    from icon_manager import IconManager
    from launcher_generator import LauncherGenerator
    from batch_configurator import BatchConfigurator
except ImportError as e:
    print(f"导入工具模块失败: {e}")
    sys.exit(1)

# 导入对话框模块
try:
    from gui_dialogs import NewInstanceDialog, BatchConfigDialog
except ImportError as e:
    print(f"导入对话框模块失败: {e}")
    # 如果导入失败，创建占位类
    class NewInstanceDialog:
        def __init__(self, *args, **kwargs):
            pass
    class BatchConfigDialog:
        def __init__(self, *args, **kwargs):
            pass


class ProcessMonitor:
    """进程监控器，用于检测浏览器实例运行状态"""
    
    def __init__(self):
        self.chrome_processes = {}
        
    def is_instance_running(self, instance_name):
        """检查指定实例是否正在运行"""
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                    cmdline = proc.info.get('cmdline', [])
                    if cmdline:
                        cmdline_str = ' '.join(cmdline)
                        if f'--user-data-dir' in cmdline_str and instance_name in cmdline_str:
                            return True
            return False
        except Exception:
            return False
    
    def get_running_instances(self):
        """获取所有正在运行的实例"""
        running = []
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                    cmdline = proc.info.get('cmdline', [])
                    if cmdline:
                        cmdline_str = ' '.join(cmdline)
                        if '--user-data-dir' in cmdline_str:
                            # 提取实例名称
                            for part in cmdline:
                                if 'instances' in part and 'Data' in part:
                                    instance_name = Path(part).parent.name
                                    if instance_name not in running:
                                        running.append(instance_name)
        except Exception:
            pass
        return running


class InstanceCard(tk.Frame):
    """实例卡片组件"""
    
    def __init__(self, parent, instance_data, on_launch=None, on_menu=None):
        super().__init__(parent, relief='raised', bd=1, bg='white')
        self.instance_data = instance_data
        self.on_launch = on_launch
        self.on_menu = on_menu
        self.is_running = False
        
        self.setup_ui()
        self.bind_events()
        
    def setup_ui(self):
        """设置界面"""
        self.configure(height=80)
        self.pack_propagate(False)
        
        # 图标区域
        self.icon_frame = tk.Frame(self, bg='white', width=70)
        self.icon_frame.pack(side='left', fill='y', padx=5)
        self.icon_frame.pack_propagate(False)
        
        # 加载图标
        self.load_icon()
        
        # 信息区域
        self.info_frame = tk.Frame(self, bg='white')
        self.info_frame.pack(side='left', fill='both', expand=True, padx=5)
        
        # 实例名称 - 从config中获取display_name
        config = self.instance_data.get('config', {})
        display_name = config.get('display_name', self.instance_data['name'])
        self.name_label = tk.Label(
            self.info_frame,
            text=display_name,
            font=('Arial', 12, 'bold'),
            bg='white',
            anchor='w'
        )
        self.name_label.pack(fill='x', pady=(5, 0))

        # 实例描述 - 从config中获取description
        description = config.get('description', '无描述')
        self.desc_label = tk.Label(
            self.info_frame,
            text=description,
            font=('Arial', 9),
            bg='white',
            fg='gray',
            anchor='w'
        )
        self.desc_label.pack(fill='x')
        
        # 状态标签
        self.status_label = tk.Label(
            self.info_frame,
            text='未运行',
            font=('Arial', 8),
            bg='white',
            fg='red',
            anchor='w'
        )
        self.status_label.pack(fill='x', pady=(0, 5))
        
        # 按钮区域
        self.button_frame = tk.Frame(self, bg='white', width=120)
        self.button_frame.pack(side='right', fill='y', padx=5)
        self.button_frame.pack_propagate(False)
        
        # 启动按钮
        self.launch_btn = tk.Button(
            self.button_frame,
            text='启动',
            command=self.launch_instance,
            bg='#4CAF50',
            fg='white',
            font=('Arial', 10, 'bold'),
            relief='flat',
            width=8
        )
        self.launch_btn.pack(pady=(10, 5))
        
        # 菜单按钮
        self.menu_btn = tk.Button(
            self.button_frame,
            text='管理',
            command=self.show_menu,
            bg='#2196F3',
            fg='white',
            font=('Arial', 9),
            relief='flat',
            width=8
        )
        self.menu_btn.pack(pady=5)
        
    def load_icon(self):
        """加载实例图标"""
        try:
            # 从config中获取图标名称
            config = self.instance_data.get('config', {})
            icon_name = config.get('icon', 'default')

            # 处理图标名称，移除可能存在的.ico后缀
            if icon_name.endswith('.ico'):
                icon_name = icon_name[:-4]

            icon_path = Path(__file__).parent / "icons" / f"{icon_name}.ico"
            if not icon_path.exists():
                icon_path = Path(__file__).parent / "icons" / "default.ico"
            
            if icon_path.exists():
                # 使用PIL加载图标
                img = Image.open(icon_path)
                img = img.resize((48, 48), Image.Resampling.LANCZOS)
                self.icon_photo = ImageTk.PhotoImage(img)
                
                self.icon_label = tk.Label(
                    self.icon_frame,
                    image=self.icon_photo,
                    bg='white'
                )
                self.icon_label.pack(expand=True)
            else:
                # 默认图标
                self.icon_label = tk.Label(
                    self.icon_frame,
                    text='🌐',
                    font=('Arial', 24),
                    bg='white'
                )
                self.icon_label.pack(expand=True)
        except Exception as e:
            # 出错时使用文字图标
            self.icon_label = tk.Label(
                self.icon_frame,
                text='🌐',
                font=('Arial', 24),
                bg='white'
            )
            self.icon_label.pack(expand=True)
    
    def bind_events(self):
        """绑定事件"""
        # 双击启动
        self.bind('<Double-Button-1>', lambda e: self.launch_instance())
        self.name_label.bind('<Double-Button-1>', lambda e: self.launch_instance())
        self.desc_label.bind('<Double-Button-1>', lambda e: self.launch_instance())
        
        # 右键菜单
        self.bind('<Button-3>', self.show_context_menu)
        self.name_label.bind('<Button-3>', self.show_context_menu)
        self.desc_label.bind('<Button-3>', self.show_context_menu)
        
    def launch_instance(self):
        """启动实例"""
        if self.on_launch:
            self.on_launch(self.instance_data['name'])
    
    def show_menu(self):
        """显示管理菜单"""
        if self.on_menu:
            self.on_menu(self.instance_data['name'])
    
    def show_context_menu(self, event):
        """显示右键菜单"""
        menu = tk.Menu(self, tearoff=0)
        menu.add_command(label="启动实例", command=self.launch_instance)
        menu.add_separator()
        menu.add_command(label="编辑实例", command=lambda: self.on_menu(self.instance_data['name']))
        menu.add_command(label="复制实例", command=lambda: self.copy_instance_from_card())
        menu.add_separator()
        menu.add_command(label="删除实例", command=lambda: self.delete_instance())
        menu.add_separator()
        menu.add_command(label="查看详情", command=lambda: self.show_details())

        try:
            menu.tk_popup(event.x_root, event.y_root)
        finally:
            menu.grab_release()

    def copy_instance_from_card(self):
        """从卡片复制实例"""
        try:
            from gui_dialogs import CopyInstanceDialog

            # 获取主窗口引用
            root_window = self.winfo_toplevel()

            dialog = CopyInstanceDialog(
                root_window,
                self.instance_data['name'],
                on_success=lambda: self.refresh_instances_callback()
            )

        except Exception as e:
            messagebox.showerror("错误", f"打开复制对话框失败: {e}")

    def refresh_instances_callback(self):
        """刷新实例回调"""
        # 向上查找BrowserLauncherGUI实例
        parent = self.master
        while parent and not hasattr(parent, 'refresh_instances'):
            parent = parent.master

        if parent and hasattr(parent, 'refresh_instances'):
            parent.refresh_instances()
    
    def delete_instance(self):
        """删除实例"""
        result = messagebox.askyesno(
            "确认删除",
            f"确定要删除实例 '{self.instance_data['name']}' 吗？\n此操作不可恢复！"
        )
        if result:
            # 这里应该调用删除功能
            pass
    
    def show_details(self):
        """显示实例详情"""
        details = f"""实例详情:
        
名称: {self.instance_data['name']}
显示名称: {self.instance_data.get('display_name', '未设置')}
描述: {self.instance_data.get('description', '无描述')}
主页: {self.instance_data.get('homepage', '未设置')}
图标: {self.instance_data.get('icon', 'default')}
创建时间: {self.instance_data.get('created_date', '未知')}
最后修改: {self.instance_data.get('last_modified', '未知')}
状态: {'运行中' if self.is_running else '未运行'}"""
        
        messagebox.showinfo("实例详情", details)
    
    def update_status(self, is_running):
        """更新运行状态"""
        self.is_running = is_running
        if is_running:
            self.status_label.config(text='运行中', fg='green')
            self.launch_btn.config(text='运行中', state='disabled', bg='gray')
        else:
            self.status_label.config(text='未运行', fg='red')
            self.launch_btn.config(text='启动', state='normal', bg='#4CAF50')


class BrowserLauncherGUI:
    """主界面类"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.setup_managers()
        self.setup_ui()
        self.load_instances()
        self.start_status_monitor()
        
    def setup_window(self):
        """设置主窗口"""
        self.root.title("浏览器多账号绿色版 v0.002")
        self.root.geometry("800x600")
        self.root.minsize(600, 400)
        
        # 设置图标
        try:
            icon_path = Path(__file__).parent / "icons" / "default.ico"
            if icon_path.exists():
                self.root.iconbitmap(str(icon_path))
        except:
            pass
    
    def setup_managers(self):
        """初始化管理器"""
        self.browser_manager = BrowserManager()
        self.icon_manager = IconManager()
        self.launcher_generator = LauncherGenerator()
        self.batch_configurator = BatchConfigurator()
        self.process_monitor = ProcessMonitor()
        
        self.instance_cards = {}
        
    def setup_ui(self):
        """设置用户界面"""
        # 创建主框架
        self.main_frame = tk.Frame(self.root)
        self.main_frame.pack(fill='both', expand=True)
        
        # 顶部标题栏
        self.create_title_bar()
        
        # 工具栏
        self.create_toolbar()
        
        # 主显示区域
        self.create_main_area()
        
        # 状态栏
        self.create_status_bar()
    
    def create_title_bar(self):
        """创建标题栏"""
        title_frame = tk.Frame(self.main_frame, bg='#1976D2', height=60)
        title_frame.pack(fill='x')
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(
            title_frame,
            text="浏览器多账号绿色版",
            font=('Arial', 16, 'bold'),
            bg='#1976D2',
            fg='white'
        )
        title_label.pack(side='left', padx=20, pady=15)
        
        version_label = tk.Label(
            title_frame,
            text="v0.002",
            font=('Arial', 10),
            bg='#1976D2',
            fg='#BBDEFB'
        )
        version_label.pack(side='right', padx=20, pady=20)
    
    def create_toolbar(self):
        """创建工具栏"""
        toolbar = tk.Frame(self.main_frame, bg='#F5F5F5', height=40)
        toolbar.pack(fill='x')
        toolbar.pack_propagate(False)
        
        # 刷新按钮
        refresh_btn = tk.Button(
            toolbar,
            text="刷新",
            command=self.refresh_instances,
            bg='#4CAF50',
            fg='white',
            relief='flat',
            padx=15
        )
        refresh_btn.pack(side='left', padx=5, pady=5)
        
        # 新建实例按钮
        new_btn = tk.Button(
            toolbar,
            text="新建实例",
            command=self.create_new_instance,
            bg='#2196F3',
            fg='white',
            relief='flat',
            padx=15
        )
        new_btn.pack(side='left', padx=5, pady=5)
        
        # 批量配置按钮
        batch_btn = tk.Button(
            toolbar,
            text="批量配置",
            command=self.batch_configure,
            bg='#FF9800',
            fg='white',
            relief='flat',
            padx=15
        )
        batch_btn.pack(side='left', padx=5, pady=5)
        
        # 设置按钮
        settings_btn = tk.Button(
            toolbar,
            text="设置",
            command=self.show_settings,
            bg='#9C27B0',
            fg='white',
            relief='flat',
            padx=15
        )
        settings_btn.pack(side='left', padx=5, pady=5)
        
        # 帮助按钮
        help_btn = tk.Button(
            toolbar,
            text="帮助",
            command=self.show_help,
            bg='#607D8B',
            fg='white',
            relief='flat',
            padx=15
        )
        help_btn.pack(side='right', padx=5, pady=5)
    
    def create_main_area(self):
        """创建主显示区域"""
        # 创建主容器框架
        main_container = tk.Frame(self.main_frame, bg='#FAFAFA')
        main_container.pack(fill='both', expand=True, padx=5, pady=5)

        # 创建滚动框架
        self.canvas = tk.Canvas(main_container, bg='#FAFAFA', highlightthickness=0)
        scrollbar = ttk.Scrollbar(main_container, orient="vertical", command=self.canvas.yview)
        self.scrollable_frame = tk.Frame(self.canvas, bg='#FAFAFA')

        # 配置滚动
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )

        # 创建窗口
        self.canvas_window = self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=scrollbar.set)

        # 绑定canvas大小变化
        def configure_scroll_region(event):
            self.canvas.configure(scrollregion=self.canvas.bbox("all"))
            # 让scrollable_frame填满canvas的宽度
            canvas_width = event.width
            self.canvas.itemconfig(self.canvas_window, width=canvas_width)

        self.canvas.bind('<Configure>', configure_scroll_region)

        # 布局
        self.canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # 绑定鼠标滚轮
        def _on_mousewheel(event):
            self.canvas.yview_scroll(int(-1*(event.delta/120)), "units")

        # 绑定到canvas和scrollable_frame
        self.canvas.bind("<MouseWheel>", _on_mousewheel)
        self.scrollable_frame.bind("<MouseWheel>", _on_mousewheel)
    
    def create_status_bar(self):
        """创建状态栏"""
        self.status_frame = tk.Frame(self.main_frame, bg='#E0E0E0', height=25)
        self.status_frame.pack(fill='x', side='bottom')
        self.status_frame.pack_propagate(False)
        
        self.status_label = tk.Label(
            self.status_frame,
            text="就绪",
            bg='#E0E0E0',
            anchor='w'
        )
        self.status_label.pack(side='left', padx=10)
        
        self.instance_count_label = tk.Label(
            self.status_frame,
            text="总实例: 0",
            bg='#E0E0E0'
        )
        self.instance_count_label.pack(side='right', padx=10)
        
        self.running_count_label = tk.Label(
            self.status_frame,
            text="运行中: 0",
            bg='#E0E0E0'
        )
        self.running_count_label.pack(side='right', padx=10)

    def load_instances(self):
        """加载所有实例"""
        try:
            instances = self.browser_manager.list_instances()

            # 清除现有卡片
            for widget in self.scrollable_frame.winfo_children():
                widget.destroy()
            self.instance_cards.clear()

            if not instances:
                # 显示空状态
                empty_label = tk.Label(
                    self.scrollable_frame,
                    text="暂无浏览器实例\n点击'新建实例'开始创建",
                    font=('Arial', 12),
                    bg='#FAFAFA',
                    fg='gray'
                )
                empty_label.pack(expand=True, pady=50)
                return

            # 创建实例卡片
            for instance in instances:
                card = InstanceCard(
                    self.scrollable_frame,
                    instance,
                    on_launch=self.launch_instance,
                    on_menu=self.manage_instance
                )
                card.pack(fill='x', padx=20, pady=5)
                self.instance_cards[instance['name']] = card

            self.update_status_counts()

            # 强制更新界面
            self.root.update_idletasks()

        except Exception as e:
            messagebox.showerror("错误", f"加载实例失败: {e}")

    def refresh_instances(self):
        """刷新实例列表"""
        self.status_label.config(text="正在刷新...")
        self.root.update()
        self.load_instances()
        self.status_label.config(text="刷新完成")
        self.root.after(2000, lambda: self.status_label.config(text="就绪"))

    def launch_instance(self, instance_name):
        """启动浏览器实例"""
        try:
            self.status_label.config(text=f"正在启动 {instance_name}...")
            self.root.update()

            # 使用线程启动，避免界面冻结
            def launch_thread():
                try:
                    # 获取实例目录
                    instance_dir = Path(__file__).parent / "instances" / instance_name
                    if not instance_dir.exists():
                        raise Exception(f"实例目录不存在: {instance_name}")

                    # 读取配置
                    config_path = instance_dir / "config.json"
                    if config_path.exists():
                        with open(config_path, 'r', encoding='utf-8') as f:
                            config = json.load(f)
                    else:
                        config = {}

                    # 构建Chrome启动命令
                    chrome_path = self.find_chrome_executable()
                    if not chrome_path:
                        raise Exception("未找到Chrome可执行文件")

                    data_dir = instance_dir / "Data"

                    cmd = [
                        str(chrome_path),
                        f"--user-data-dir={data_dir}",
                        "--no-first-run",
                        "--no-default-browser-check"
                    ]

                    # 添加主页
                    homepage = config.get('homepage')
                    if homepage:
                        cmd.append(homepage)

                    # 启动Chrome
                    subprocess.Popen(cmd, cwd=str(instance_dir))

                    # 更新状态
                    self.root.after(0, lambda: self.status_label.config(text=f"{instance_name} 启动成功"))
                    self.root.after(2000, lambda: self.status_label.config(text="就绪"))

                except Exception as e:
                    error_msg = self.get_friendly_error_message(str(e))
                    self.root.after(0, lambda: messagebox.showerror("启动失败", error_msg))
                    self.root.after(0, lambda: self.status_label.config(text="启动失败"))

            threading.Thread(target=launch_thread, daemon=True).start()

        except Exception as e:
            messagebox.showerror("错误", f"启动实例失败: {e}")
            self.status_label.config(text="启动失败")

    def manage_instance(self, instance_name):
        """管理实例"""
        # 显示管理菜单
        menu = tk.Menu(self.root, tearoff=0)
        menu.add_command(label="编辑实例", command=lambda: self.edit_instance(instance_name))
        menu.add_command(label="复制实例", command=lambda: self.copy_instance(instance_name))
        menu.add_separator()
        menu.add_command(label="删除实例", command=lambda: self.delete_instance(instance_name))
        menu.add_separator()
        menu.add_command(label="打开数据目录", command=lambda: self.open_data_dir(instance_name))

        # 在鼠标位置显示菜单
        try:
            menu.tk_popup(self.root.winfo_pointerx(), self.root.winfo_pointery())
        finally:
            menu.grab_release()

    def edit_instance(self, instance_name):
        """编辑实例"""
        try:
            from gui_dialogs import EditInstanceDialog

            dialog = EditInstanceDialog(
                self.root,
                instance_name,
                on_success=self.refresh_instances
            )

        except Exception as e:
            messagebox.showerror("错误", f"打开编辑对话框失败: {e}")

    def copy_instance(self, instance_name):
        """复制实例"""
        try:
            from gui_dialogs import CopyInstanceDialog

            dialog = CopyInstanceDialog(
                self.root,
                instance_name,
                on_success=self.refresh_instances
            )

        except Exception as e:
            messagebox.showerror("错误", f"打开复制对话框失败: {e}")

    def delete_instance(self, instance_name):
        """删除实例"""
        result = messagebox.askyesno(
            "确认删除",
            f"确定要删除实例 '{instance_name}' 吗？\n\n此操作不可撤销！"
        )

        if result:
            try:
                success = self.browser_manager.delete_instance(instance_name)
                if success:
                    messagebox.showinfo("删除成功", f"实例 '{instance_name}' 已删除")
                    self.refresh_instances()
                else:
                    messagebox.showerror("删除失败", f"删除实例 '{instance_name}' 失败")
            except Exception as e:
                messagebox.showerror("删除失败", f"删除实例失败: {e}")

    def open_data_dir(self, instance_name):
        """打开数据目录"""
        try:
            import subprocess
            import platform

            data_dir = Path(__file__).parent / "instances" / instance_name / "Data"

            if data_dir.exists():
                if platform.system() == "Windows":
                    subprocess.run(["explorer", str(data_dir)])
                elif platform.system() == "Darwin":  # macOS
                    subprocess.run(["open", str(data_dir)])
                else:  # Linux
                    subprocess.run(["xdg-open", str(data_dir)])
            else:
                messagebox.showerror("错误", "数据目录不存在")

        except Exception as e:
            messagebox.showerror("错误", f"打开目录失败: {e}")

    def create_new_instance(self):
        """创建新实例"""
        try:
            dialog = NewInstanceDialog(self.root, on_success=self.on_instance_created)
        except Exception as e:
            messagebox.showerror("错误", f"打开新建实例对话框失败: {e}")

    def on_instance_created(self, instance_data):
        """实例创建成功回调"""
        self.refresh_instances()
        messagebox.showinfo("成功", f"实例 '{instance_data['name']}' 创建成功！")

    def batch_configure(self):
        """批量配置"""
        try:
            dialog = BatchConfigDialog(self.root, on_success=self.on_batch_configured)
        except Exception as e:
            messagebox.showerror("错误", f"打开批量配置对话框失败: {e}")

    def on_batch_configured(self):
        """批量配置成功回调"""
        self.refresh_instances()

    def show_settings(self):
        """显示设置"""
        settings_window = tk.Toplevel(self.root)
        settings_window.title("设置")
        settings_window.geometry("400x300")
        settings_window.resizable(False, False)
        settings_window.transient(self.root)
        settings_window.grab_set()

        # 居中显示
        x = (settings_window.winfo_screenwidth() // 2) - (400 // 2)
        y = (settings_window.winfo_screenheight() // 2) - (300 // 2)
        settings_window.geometry(f"400x300+{x}+{y}")

        # 设置内容
        main_frame = tk.Frame(settings_window, padx=20, pady=20)
        main_frame.pack(fill='both', expand=True)

        tk.Label(main_frame, text="程序设置", font=('Arial', 14, 'bold')).pack(pady=(0, 20))

        # Chrome路径设置
        chrome_frame = tk.Frame(main_frame)
        chrome_frame.pack(fill='x', pady=10)

        tk.Label(chrome_frame, text="Chrome路径:", width=12, anchor='w').pack(side='left')
        chrome_path = self.find_chrome_executable()
        chrome_label = tk.Label(chrome_frame, text=str(chrome_path) if chrome_path else "未找到",
                               fg='green' if chrome_path else 'red')
        chrome_label.pack(side='left', fill='x', expand=True)

        # 状态监控设置
        monitor_frame = tk.Frame(main_frame)
        monitor_frame.pack(fill='x', pady=10)

        tk.Label(monitor_frame, text="状态监控:", width=12, anchor='w').pack(side='left')
        tk.Label(monitor_frame, text="每3秒检查一次", fg='blue').pack(side='left')

        # 关于信息
        about_frame = tk.Frame(main_frame)
        about_frame.pack(fill='x', pady=20)

        about_text = """浏览器多账号绿色版 v1.0

基于Chrome Portable的多账号管理工具
支持完全隔离的浏览器实例管理

开发者：Augment Agent
技术支持：github.com/augment-code"""

        tk.Label(about_frame, text=about_text, justify='left', fg='gray').pack()

        # 关闭按钮
        close_btn = tk.Button(main_frame, text="关闭", command=settings_window.destroy, width=10)
        close_btn.pack(pady=20)

    def show_help(self):
        """显示帮助"""
        help_text = """浏览器多账号绿色版 v0.002

使用说明:
1. 双击实例卡片启动浏览器
2. 点击'启动'按钮启动实例
3. 右键点击实例显示管理菜单
4. 使用工具栏按钮进行管理操作

功能特色:
- 完全数据隔离
- 跨电脑可移植
- 自定义图标
- 批量配置

技术支持: 查看README.md文档"""

        messagebox.showinfo("帮助", help_text)

    def find_chrome_executable(self):
        """查找Chrome可执行文件"""
        # 可能的Chrome路径
        possible_paths = [
            # Chrome Portable
            Path(__file__).parent / "GoogleChromePortable" / "App" / "Chrome-bin" / "chrome.exe",
            Path(__file__).parent / "GoogleChromePortable" / "GoogleChromePortable.exe",
            # 系统安装的Chrome
            Path("C:/Program Files/Google/Chrome/Application/chrome.exe"),
            Path("C:/Program Files (x86)/Google/Chrome/Application/chrome.exe"),
            # 用户目录下的Chrome
            Path.home() / "AppData/Local/Google/Chrome/Application/chrome.exe"
        ]

        for path in possible_paths:
            if path.exists():
                return path

        return None

    def get_friendly_error_message(self, error_str):
        """获取友好的错误信息"""
        if "未找到Chrome可执行文件" in error_str:
            return ("未找到Chrome浏览器！\n\n"
                   "请确保：\n"
                   "1. 已下载Chrome Portable并解压到项目目录\n"
                   "2. 或者系统已安装Chrome浏览器\n"
                   "3. 检查GoogleChromePortable目录是否存在")
        elif "实例目录不存在" in error_str:
            return ("实例不存在或已损坏！\n\n"
                   "请尝试：\n"
                   "1. 刷新实例列表\n"
                   "2. 重新创建该实例")
        elif "Permission denied" in error_str or "拒绝访问" in error_str:
            return ("权限不足！\n\n"
                   "请尝试：\n"
                   "1. 以管理员身份运行程序\n"
                   "2. 检查文件夹权限设置")
        else:
            return f"启动失败：{error_str}\n\n请检查配置或联系技术支持。"

    def start_status_monitor(self):
        """启动状态监控"""
        def monitor_thread():
            while True:
                try:
                    running_instances = self.process_monitor.get_running_instances()

                    # 更新卡片状态
                    for instance_name, card in self.instance_cards.items():
                        is_running = instance_name in running_instances
                        self.root.after(0, lambda c=card, r=is_running: c.update_status(r))

                    # 更新状态栏
                    running_count = len(running_instances)
                    total_count = len(self.instance_cards)

                    self.root.after(0, lambda: self.running_count_label.config(text=f"运行中: {running_count}"))

                    time.sleep(3)  # 每3秒检查一次

                except Exception as e:
                    # 检查是否是因为主窗口关闭导致的错误
                    try:
                        self.root.winfo_exists()
                        print(f"状态监控错误: {e}")
                        time.sleep(5)
                    except:
                        # 主窗口已关闭，退出监控线程
                        break

        threading.Thread(target=monitor_thread, daemon=True).start()

    def update_status_counts(self):
        """更新状态栏计数"""
        total_count = len(self.instance_cards)
        self.instance_count_label.config(text=f"总实例: {total_count}")

    def run(self):
        """运行主界面"""
        self.root.mainloop()


def main():
    """主函数"""
    try:
        # 检查必要文件
        required_dirs = ['instances', 'icons', 'tools', 'GoogleChromePortable']
        for dir_name in required_dirs:
            dir_path = Path(__file__).parent / dir_name
            if not dir_path.exists():
                messagebox.showerror("错误", f"缺少必要目录: {dir_name}")
                return

        # 启动GUI
        app = BrowserLauncherGUI()
        app.run()

    except Exception as e:
        messagebox.showerror("启动失败", f"程序启动失败: {e}")


if __name__ == "__main__":
    main()
