#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
from pathlib import Path

def find_chrome_executable():
    """查找Chrome可执行文件"""
    print("🔍 开始查找Chrome可执行文件...")
    
    if sys.platform == "win32":
        username = os.getenv('USERNAME', '')
        print(f"📋 当前用户: {username}")
        
        possible_paths = [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
            f"C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\Application\\chrome.exe",
            f"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\Application\\chrome.exe",
            "./chrome-win/chrome.exe",  # 便携版路径
            "./GoogleChromePortable/App/Chrome-bin/chrome.exe",  # 便携版路径
            "./chrome/chrome.exe",  # 项目目录下的Chrome
            "./Chrome/chrome.exe",  # 项目目录下的Chrome
            "chrome.exe"  # 当前目录
        ]
        
        print(f"🎯 检查 {len(possible_paths)} 个可能的路径:")
        for i, path in enumerate(possible_paths, 1):
            print(f"  {i}. {path}")
            if Path(path).exists():
                print(f"    ✅ 找到: {path}")
                return path
            else:
                print(f"    ❌ 不存在")
        
        print("❌ 未找到Chrome可执行文件")
        return None
    
    else:
        # Linux/macOS
        possible_paths = [
            "/usr/bin/google-chrome",
            "/usr/bin/chromium-browser",
            "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
        ]
        
        for path in possible_paths:
            if Path(path).exists():
                print(f"✅ 找到Chrome: {path}")
                return path
        
        print("❌ 未找到Chrome可执行文件")
        return None

def test_chrome_launch():
    """测试Chrome启动"""
    chrome_path = find_chrome_executable()
    
    if not chrome_path:
        print("❌ 无法测试启动，因为未找到Chrome")
        return False
    
    print(f"\n🚀 测试启动Chrome: {chrome_path}")
    
    try:
        import subprocess
        
        # 创建测试用户数据目录
        test_data_dir = Path("./test_user_data")
        test_data_dir.mkdir(exist_ok=True)
        
        cmd = [
            chrome_path,
            f"--user-data-dir={test_data_dir}",
            "--no-first-run",
            "--no-default-browser-check",
            "--disable-background-timer-throttling",
            "--new-window",
            "about:blank"
        ]
        
        print(f"📋 启动命令: {' '.join(cmd)}")
        
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.DEVNULL,
            stderr=subprocess.DEVNULL,
            creationflags=subprocess.CREATE_NEW_PROCESS_GROUP if sys.platform == "win32" else 0
        )
        
        print(f"✅ Chrome进程已启动，PID: {process.pid}")
        print("💡 请手动检查Chrome是否正常打开")
        print("⚠️ 测试完成后请手动关闭Chrome窗口")
        
        return True
        
    except Exception as e:
        print(f"❌ 启动Chrome失败: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🔧 Chrome路径检测和启动测试")
    print("=" * 60)
    
    # 显示当前工作目录
    print(f"📁 当前工作目录: {os.getcwd()}")
    print(f"🖥️ 操作系统: {sys.platform}")
    print()
    
    # 查找Chrome
    chrome_path = find_chrome_executable()
    
    if chrome_path:
        print(f"\n✅ 成功找到Chrome: {chrome_path}")
        
        # 测试启动
        if input("\n是否测试启动Chrome? (y/N): ").lower() == 'y':
            test_chrome_launch()
    else:
        print("\n❌ 未找到Chrome，请:")
        print("  1. 安装Google Chrome浏览器")
        print("  2. 或下载Chrome便携版到项目目录")
        print("  3. 或确保Chrome在PATH环境变量中")
    
    print("\n" + "=" * 60)
    print("🏁 测试完成")
    print("=" * 60)
