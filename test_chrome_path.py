#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
from pathlib import Path

# 添加tools目录到Python路径
sys.path.append(str(Path(__file__).parent / "tools"))

from browser_manager import BrowserManager

def test_chrome_path():
    """测试Chrome路径检测"""
    print("=" * 60)
    print("🔧 Chrome路径检测测试")
    print("=" * 60)
    
    # 创建BrowserManager实例
    manager = BrowserManager()
    
    # 测试Chrome路径检测
    print("🔍 开始查找Chrome可执行文件...")
    chrome_path = manager._find_chrome_executable()
    
    if chrome_path:
        print(f"✅ 找到Chrome: {chrome_path}")
        
        # 验证文件是否存在
        if Path(chrome_path).exists():
            print(f"✅ 文件存在: {chrome_path}")
            
            # 获取文件信息
            file_path = Path(chrome_path)
            print(f"📁 文件大小: {file_path.stat().st_size / 1024 / 1024:.1f} MB")
            print(f"📅 修改时间: {file_path.stat().st_mtime}")
            
        else:
            print(f"❌ 文件不存在: {chrome_path}")
    else:
        print("❌ 未找到Chrome可执行文件")
        
        # 显示检查的路径
        print("\n🔍 检查的路径:")
        username = os.getenv('USERNAME', '')
        possible_paths = [
            # 便携版路径（优先检查）
            "./GoogleChromePortable/App/Chrome-bin/chrome.exe",
            "./chrome-win/chrome.exe",
            "./chrome/chrome.exe",
            "./Chrome/chrome.exe",
            "chrome.exe",
            # 系统安装路径
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
            f"C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\Application\\chrome.exe",
            f"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\Application\\chrome.exe"
        ]
        
        for i, path in enumerate(possible_paths, 1):
            exists = Path(path).exists()
            status = "✅ 存在" if exists else "❌ 不存在"
            print(f"  {i:2d}. {status} - {path}")
    
    print("\n" + "=" * 60)
    print("🏁 测试完成")
    print("=" * 60)

if __name__ == "__main__":
    test_chrome_path()
