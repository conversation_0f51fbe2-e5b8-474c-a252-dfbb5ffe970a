#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import subprocess
import sys
import os
from pathlib import Path

def test_chrome_simple():
    """简单测试Chrome启动"""
    print("=" * 60)
    print("🧪 简单Chrome启动测试")
    print("=" * 60)
    
    # Chrome路径
    chrome_path = "./GoogleChromePortable/App/Chrome-bin/chrome.exe"
    
    print(f"🔍 Chrome路径: {chrome_path}")
    
    # 检查文件是否存在
    if not Path(chrome_path).exists():
        print(f"❌ Chrome文件不存在: {chrome_path}")
        return False
    
    print("✅ Chrome文件存在")
    
    # 创建测试用户数据目录
    test_data_dir = Path("./test_user_data")
    test_data_dir.mkdir(exist_ok=True)
    
    print(f"📁 测试数据目录: {test_data_dir}")
    
    # 构建启动命令
    cmd = [
        chrome_path,
        f"--user-data-dir={test_data_dir}",
        "--no-first-run",
        "--no-default-browser-check",
        "--disable-background-timer-throttling",
        "--new-window",
        "about:blank"
    ]
    
    print(f"🎯 启动命令: {' '.join(cmd)}")
    
    try:
        print("🚀 启动Chrome...")
        
        # 启动进程
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            creationflags=subprocess.CREATE_NEW_PROCESS_GROUP if sys.platform == "win32" else 0
        )
        
        print(f"✅ Chrome进程已启动，PID: {process.pid}")
        
        # 等待一下看是否有错误
        import time
        time.sleep(3)
        
        # 检查进程状态
        poll_result = process.poll()
        if poll_result is None:
            print("✅ Chrome进程正在运行")
            print("💡 请检查是否有Chrome窗口打开")
            print("⚠️ 测试完成后请手动关闭Chrome")
            return True
        else:
            print(f"❌ Chrome进程已退出，退出码: {poll_result}")
            
            # 读取错误输出
            stdout, stderr = process.communicate()
            if stdout:
                print(f"📋 标准输出: {stdout.decode('utf-8', errors='ignore')}")
            if stderr:
                print(f"📋 错误输出: {stderr.decode('utf-8', errors='ignore')}")
            
            return False
            
    except Exception as e:
        print(f"❌ 启动Chrome失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("\n" + "=" * 60)
    print("🏁 测试完成")
    print("=" * 60)

if __name__ == "__main__":
    test_chrome_simple()
